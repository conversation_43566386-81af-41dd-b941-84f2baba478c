"""
UI状态管理器

统一管理Streamlit UI的状态，解决流式更新时的状态管理混乱问题。
"""

from typing import Dict, Any, List, Optional
from datetime import datetime
import streamlit as st
from dataclasses import dataclass, field
import logging

logger = logging.getLogger(__name__)


@dataclass
class ExecutionStep:
    """执行步骤数据类"""
    timestamp: datetime
    node: str  # planner, web, validator
    action: str
    content: str
    status: str  # pending, running, completed, error
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "timestamp": self.timestamp.isoformat(),
            "node": self.node,
            "action": self.action,
            "content": self.content,
            "status": self.status
        }


@dataclass
class WorkflowState:
    """工作流状态"""
    current_node: Optional[str] = None
    current_status: str = "idle"  # idle, running, completed, error
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    execution_steps: List[ExecutionStep] = field(default_factory=list)
    final_result: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    
    @property
    def execution_time(self) -> float:
        """计算执行时间"""
        if self.start_time and self.end_time:
            return (self.end_time - self.start_time).total_seconds()
        elif self.start_time:
            return (datetime.now() - self.start_time).total_seconds()
        return 0
    
    @property
    def step_count(self) -> int:
        """获取步骤数量"""
        return len(self.execution_steps)
    
    def get_node_status(self, node: str) -> str:
        """获取特定节点的状态"""
        # 检查是否是当前节点
        if self.current_node == node and self.current_status == "running":
            return "running"
        
        # 检查是否已完成
        for step in self.execution_steps:
            if step.node == node and step.status == "completed":
                return "completed"
        
        # 检查是否出错
        for step in self.execution_steps:
            if step.node == node and step.status == "error":
                return "error"
                
        return "pending"


class UIStateManager:
    """
    UI状态管理器
    
    负责：
    1. 管理工作流执行状态
    2. 处理流式更新
    3. 维护执行历史
    4. 提供状态查询接口
    """
    
    def __init__(self):
        """初始化状态管理器"""
        # 使用session_state持久化状态
        if "workflow_state" not in st.session_state:
            st.session_state.workflow_state = WorkflowState()
        
        if "execution_history" not in st.session_state:
            st.session_state.execution_history = []
    
    @property
    def workflow_state(self) -> WorkflowState:
        """获取当前工作流状态"""
        return st.session_state.workflow_state
    
    @property
    def execution_history(self) -> List[Dict[str, Any]]:
        """获取执行历史"""
        return st.session_state.execution_history
    
    def start_execution(self, task_description: str):
        """开始执行任务"""
        logger.info(f"Starting execution: {task_description}")
        
        # 重置状态
        st.session_state.workflow_state = WorkflowState(
            current_status="running",
            start_time=datetime.now()
        )
        
        # 添加初始步骤
        self.add_step(
            node="system",
            action="start",
            content=f"开始执行任务: {task_description}",
            status="completed"
        )
    
    def update_from_stream(self, stream_data: Dict[str, Any]):
        """
        从流式数据更新状态
        
        智能合并策略，避免数据重复和丢失
        """
        try:
            # 保存原始流数据用于最终结果
            if not hasattr(self.workflow_state, '_raw_stream_data'):
                self.workflow_state._raw_stream_data = {}
            
            # 合并流数据
            for key, value in stream_data.items():
                if key in self.workflow_state._raw_stream_data:
                    # 智能合并
                    if isinstance(value, dict):
                        if "execution_results" in value and isinstance(value["execution_results"], list):
                            # 合并执行结果列表
                            existing = self.workflow_state._raw_stream_data.get(key, {})
                            if "execution_results" not in existing:
                                existing["execution_results"] = []
                            existing["execution_results"].extend(value["execution_results"])
                            self.workflow_state._raw_stream_data[key] = existing
                        else:
                            # 覆盖更新
                            self.workflow_state._raw_stream_data[key].update(value)
                    else:
                        self.workflow_state._raw_stream_data[key] = value
                else:
                    self.workflow_state._raw_stream_data[key] = value
            
            # 识别当前节点
            current_node = self._identify_current_node(stream_data)
            if current_node:
                self.workflow_state.current_node = current_node
                logger.debug(f"Current node: {current_node}")
            
            # 提取并添加新步骤
            new_steps = self._extract_steps_from_stream(stream_data)
            for step in new_steps:
                self.add_step(**step)
            
            # 更新final_result以便结果展示
            self.workflow_state.final_result = self.workflow_state._raw_stream_data
            
            # 检查是否完成
            if self._check_completion(stream_data):
                self.complete_execution(self.workflow_state._raw_stream_data)
                
        except Exception as e:
            logger.error(f"Error updating from stream: {e}")
            self.set_error(str(e))
    
    def add_step(self, node: str, action: str, content: str, status: str = "completed"):
        """添加执行步骤"""
        step = ExecutionStep(
            timestamp=datetime.now(),
            node=node,
            action=action,
            content=content[:500],  # 限制内容长度
            status=status
        )
        
        self.workflow_state.execution_steps.append(step)
        logger.debug(f"Added step: {node}/{action}")
    
    def complete_execution(self, result: Optional[Dict[str, Any]] = None):
        """完成执行"""
        self.workflow_state.current_status = "completed"
        self.workflow_state.end_time = datetime.now()
        self.workflow_state.final_result = result
        
        # 添加到历史记录
        self._save_to_history()
        
        logger.info("Execution completed")
    
    def set_error(self, error_message: str):
        """设置错误状态"""
        self.workflow_state.current_status = "error"
        self.workflow_state.error_message = error_message
        self.workflow_state.end_time = datetime.now()
        
        # 添加错误步骤
        self.add_step(
            node="system",
            action="error",
            content=error_message,
            status="error"
        )
        
        # 保存到历史
        self._save_to_history()
        
        logger.error(f"Execution error: {error_message}")
    
    def reset(self):
        """重置状态"""
        st.session_state.workflow_state = WorkflowState()
        logger.info("State reset")
    
    def clear_history(self):
        """清空历史记录"""
        st.session_state.execution_history = []
        logger.info("History cleared")
    
    # 私有方法
    
    def _identify_current_node(self, stream_data: Dict[str, Any]) -> Optional[str]:
        """识别当前执行节点"""
        # LangGraph的节点名称映射
        node_mapping = {
            "planner": "planner",
            "web": "web",
            "validator": "validator"
        }
        
        for key in stream_data.keys():
            if key in node_mapping:
                return node_mapping[key]
        
        return None
    
    def _extract_steps_from_stream(self, stream_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """从流式数据中提取步骤"""
        steps = []
        
        for node_name, node_data in stream_data.items():
            if isinstance(node_data, dict):
                # 提取消息
                if "messages" in node_data and node_data["messages"]:
                    last_message = node_data["messages"][-1]
                    if hasattr(last_message, "content"):
                        steps.append({
                            "node": node_name,
                            "action": "message",
                            "content": last_message.content,
                            "status": "completed"
                        })
                
                # 提取执行结果
                if "execution_results" in node_data:
                    for result in node_data["execution_results"]:
                        if not self._is_duplicate_result(result):
                            agent = result.get("agent", "unknown")
                            output = result.get("result", {}).get("output", "")
                            if output:
                                steps.append({
                                    "node": agent,
                                    "action": "execution",
                                    "content": output,
                                    "status": "completed"
                                })
        
        return steps
    
    def _is_duplicate_result(self, result: Dict[str, Any]) -> bool:
        """检查是否是重复的结果"""
        # 简单的去重逻辑，可以根据需要增强
        result_str = str(result)
        for step in self.workflow_state.execution_steps[-5:]:  # 只检查最近5个步骤
            if result_str in step.content:
                return True
        return False
    
    def _check_completion(self, stream_data: Dict[str, Any]) -> bool:
        """检查是否完成"""
        for node_data in stream_data.values():
            if isinstance(node_data, dict):
                if node_data.get("is_complete") == True:
                    return True
                if node_data.get("final_status") == "completed":
                    return True
        return False
    
    def _save_to_history(self):
        """保存到历史记录"""
        history_entry = {
            "timestamp": self.workflow_state.start_time.isoformat() if self.workflow_state.start_time else None,
            "execution_time": self.workflow_state.execution_time,
            "step_count": self.workflow_state.step_count,
            "status": self.workflow_state.current_status,
            "error": self.workflow_state.error_message,
            "result": self.workflow_state.final_result
        }
        
        st.session_state.execution_history.append(history_entry)
        
        # 限制历史记录数量
        if len(st.session_state.execution_history) > 50:
            st.session_state.execution_history = st.session_state.execution_history[-50:]


# 全局状态管理器实例
_state_manager = None

def get_state_manager() -> UIStateManager:
    """获取全局状态管理器实例"""
    global _state_manager
    if _state_manager is None:
        _state_manager = UIStateManager()
    return _state_manager