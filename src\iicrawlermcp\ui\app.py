"""
iICrawlerMCP Streamlit UI 主应用 - 重构版

基于LangGraph工作流的智能网页爬虫系统，提供清晰的可视化界面。

功能特性：
- LangGraph工作流可视化
- 实时执行进度跟踪
- 智能结果展示
- 状态管理优化
"""

import asyncio
import sys

# Windows 平台事件循环策略修复
if sys.platform.lower().startswith("win"):
    asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())

import streamlit as st
import os
from datetime import datetime
from typing import Dict, Any, Optional
import traceback
import logging

# 添加项目根目录到 Python 路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), "../../.."))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

try:
    # 核心模块
    from src.iicrawlermcp.core.orchestration_graph import execute_with_streaming
    from src.iicrawlermcp.core.config import Config
    
    # UI组件
    from src.iicrawlermcp.ui.components import (
        render_workflow_visualizer,
        render_results_display
    )
    
    # 状态管理
    from src.iicrawlermcp.ui.state_manager import get_state_manager
    
    # 样式管理
    from src.iicrawlermcp.ui.styles import apply_theme
    
    # 工具函数
    from src.iicrawlermcp.ui.utils.helpers import (
        validate_config,
        load_task_templates,
    )
except ImportError as e:
    st.error(f"❌ 导入模块失败: {e}")
    st.error("请确保已正确安装项目依赖")
    st.stop()

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 页面配置
st.set_page_config(
    page_title="iICrawlerMCP - 智能网页爬虫",
    page_icon="🕷️",
    layout="wide",
    initial_sidebar_state="expanded",
)

# 应用自定义主题
apply_theme()


def render_header():
    """渲染页面头部 - 新设计"""
    st.markdown('<h1 class="main-header">🕷️ iICrawlerMCP</h1>', unsafe_allow_html=True)
    st.markdown(
        '<p class="sub-header">智能网页爬虫系统 - 基于LangGraph工作流架构</p>',
        unsafe_allow_html=True,
    )
    
    # 快速操作提示
    col1, col2 = st.columns([3, 1])
    with col1:
        st.info("💡 **提示**: 任务执行中可随时刷新页面(F5)中断 | 支持实时查看LangGraph工作流进度")
    with col2:
        if st.button("🔄 重置系统", type="secondary"):
            get_state_manager().reset()
            st.rerun()


def render_sidebar():
    """渲染侧边栏 - 新设计"""
    st.sidebar.header("⚙️ 系统配置")
    
    # 配置验证
    config_status = validate_config()
    
    # 使用状态徽章显示
    if config_status["valid"]:
        st.sidebar.success("✅ 配置验证通过")
    else:
        st.sidebar.error(f"❌ 配置错误: {config_status['error']}")
    
    # 配置详情（使用expander折叠）
    with st.sidebar.expander("📋 查看配置详情"):
        api_key = getattr(Config, "OPENAI_API_KEY", "")
        st.text(f"API Key: {'*' * 10}...{api_key[-4:] if api_key else '未配置'}")
        st.text(f"模型: {getattr(Config, 'OPENAI_MODEL', 'gpt-3.5-turbo')}")
        st.text(f"浏览器: {'无头' if getattr(Config, 'HEADLESS', True) else '有头'}模式")
    
    # 执行历史统计
    st.sidebar.markdown("---")
    st.sidebar.header("📊 执行统计")
    state_manager = get_state_manager()
    history = state_manager.execution_history
    
    col1, col2 = st.sidebar.columns(2)
    with col1:
        st.metric("总执行次数", len(history))
    with col2:
        success_count = sum(1 for h in history if h.get("status") == "completed")
        st.metric("成功率", f"{(success_count/len(history)*100 if history else 0):.1f}%")
    
    # 快速操作
    st.sidebar.markdown("---")
    st.sidebar.header("🚀 快速操作")
    
    if st.sidebar.button("🧹 清空历史记录"):
        state_manager.clear_history()
        st.success("历史记录已清空")
    
    if st.sidebar.button("🔄 关闭浏览器"):
        try:
            from src.iicrawlermcp.core.browser import close_global_browser
            close_global_browser()
            st.success("浏览器已关闭")
        except Exception as e:
            st.error(f"关闭失败: {e}")
    
    return config_status["valid"]


def clear_context():
    """清空系统上下文"""
    try:
        # 1. 重置状态管理器
        get_state_manager().reset()
        
        # 2. 关闭浏览器
        from src.iicrawlermcp.core.browser import close_global_browser
        close_global_browser()
        
        # 3. 清理截图
        screenshots_dir = "screenshots"
        if os.path.exists(screenshots_dir):
            for file in os.listdir(screenshots_dir):
                if file.endswith((".png", ".jpg", ".jpeg")):
                    try:
                        os.remove(os.path.join(screenshots_dir, file))
                    except:
                        pass
        
        # 4. 清理工具缓存
        from src.iicrawlermcp.tools.browser_tools import cleanup_tools
        cleanup_tools()
        
        st.success("✅ 系统上下文已清空")
        logger.info("System context cleared")
        
    except Exception as e:
        st.error(f"❌ 清空失败: {e}")
        logger.error(f"Failed to clear context: {e}")


def render_task_input() -> Dict[str, Any]:
    """渲染任务输入区域 - 新设计"""
    st.markdown("### 📝 任务输入")
    
    # 任务模板选择
    col1, col2 = st.columns([2, 1])
    
    with col1:
        templates = load_task_templates()
        template_names = ["自定义任务"] + list(templates.keys())
        selected_template = st.selectbox(
            "📦 选择任务模板",
            template_names,
            help="选择预定义的任务模板或自定义任务"
        )
    
    with col2:
        # 快速操作按钮
        if st.button("🔄 清空输入", type="secondary"):
            st.session_state["task_input"] = ""
            st.rerun()
    
    # 任务描述输入
    if selected_template == "自定义任务":
        default_task = st.session_state.get(
            "task_input",
            "导航到https://www.booking.com/index.zh-cn.html,地点选择上海,时间选择8/15-8/20,点击搜索按钮,拿到前三个结果的标题和介绍,评分和价格"
        )
    else:
        default_task = templates[selected_template]["description"]
    
    task_description = st.text_area(
        "✨ 任务描述",
        value=default_task,
        height=100,
        placeholder="请使用自然语言描述您想要执行的网页操作任务...",
        help="例如：打开百度搜索'Python 最佳实践'并获取前5个结果",
        key="task_input"
    )
    
    # 高级选项（默认折叠）
    with st.expander("⚙️ 高级设置", expanded=False):
        col1, col2, col3 = st.columns(3)
        
        with col1:
            max_iterations = st.number_input(
                "最大步数",
                min_value=1,
                max_value=100,
                value=30,
                help="限制最大执行步数以避免无限循环"
            )
        
        with col2:
            timeout = st.number_input(
                "超时(秒)",
                min_value=30,
                max_value=600,
                value=180,
                help="任务执行的最大时间限制"
            )
        
        with col3:
            verbose = st.checkbox(
                "详细日志",
                value=True,
                help="显示详细的执行过程日志"
            )
    
    return {
        "task_description": task_description,
        "max_iterations": max_iterations,
        "timeout": timeout,
        "verbose": verbose,
        "template": selected_template,
    }


def render_execution_controls(task_config: Dict[str, Any]):
    """渲染执行控制区域"""
    st.markdown("### 🚀 执行控制")
    
    col1, col2, col3 = st.columns([2, 1, 1])
    
    with col1:
        # 主执行按钮
        task_ready = bool(task_config["task_description"].strip())
        is_running = get_state_manager().workflow_state.current_status == "running"
        
        if st.button(
            "🎯 开始执行" if not is_running else "⏸️ 正在执行...",
            type="primary",
            disabled=not task_ready or is_running,
            use_container_width=True
        ):
            execute_task_with_ui(task_config)
    
    with col2:
        if st.button("🧹 清空上下文", type="secondary", use_container_width=True):
            clear_context()
            st.rerun()
    
    with col3:
        if st.button("📊 查看历史", use_container_width=True):
            st.session_state["show_history"] = not st.session_state.get("show_history", False)
            st.rerun()


def execute_task_with_ui(task_config: Dict[str, Any]):
    """执行任务并更新UI"""
    task_description = task_config["task_description"]
    
    # 获取状态管理器
    state_manager = get_state_manager()
    
    # 清理旧截图
    clear_screenshots()
    
    # 开始执行
    state_manager.start_execution(task_description)
    
    # 创建占位符，避免重复显示
    workflow_placeholder = st.empty()
    status_placeholder = st.empty()
    result_placeholder = st.empty()
    
    try:
        # 初始渲染工作流
        with workflow_placeholder.container():
            render_workflow_visualizer(state_manager.workflow_state)
        
        # 使用流式执行
        with st.spinner("🔄 正在执行任务... 刷新页面(F5)可中断"):
            for state_update in execute_with_streaming(task_description):
                # 更新状态管理器
                state_manager.update_from_stream(state_update)
                
                # 更新工作流可视化
                with workflow_placeholder.container():
                    render_workflow_visualizer(state_manager.workflow_state)
                
                # 显示当前执行状态（简化版）
                with status_placeholder.container():
                    if state_manager.workflow_state.execution_steps:
                        current_step = state_manager.workflow_state.execution_steps[-1]
                        st.info(f"⚡ 正在执行: {current_step.node.upper()} - {current_step.content[:100]}...")
        
        # 完成执行
        state_manager.complete_execution(state_update if 'state_update' in locals() else None)
        
        # 显示最终结果
        with result_placeholder.container():
            render_results_display(state_manager.workflow_state)
        
        st.success(f"✅ 任务执行完成！耗时 {state_manager.workflow_state.execution_time:.2f} 秒")

    except Exception as e:
        error_msg = str(e)
        state_manager.set_error(error_msg)
        
        st.error(f"❌ 任务执行失败: {error_msg}")
        
        # 显示详细错误信息
        with st.expander("🔍 详细错误信息"):
            st.code(traceback.format_exc())
        
        logger.error(f"Task execution failed: {e}")
        logger.error(traceback.format_exc())
        
    finally:
        # 清理资源
        try:
            from src.iicrawlermcp.core.browser import close_global_browser
            close_global_browser()
        except:
            pass


def render_history():
    """渲染执行历史 - 新设计"""
    if not st.session_state.get("show_history", False):
        return
    
    st.markdown("### 📚 执行历史")
    
    state_manager = get_state_manager()
    history = state_manager.execution_history
    
    if not history:
        st.info("暂无执行历史")
        return
    
    # 显示最近的记录
    for record in reversed(history[-10:]):
        timestamp = record.get("timestamp", "N/A")
        status = record.get("status", "unknown")
        
        # 选择状态图标
        status_icon = {
            "completed": "✅",
            "error": "❌",
            "running": "🔄"
        }.get(status, "❓")
        
        with st.expander(f"{status_icon} {timestamp} - {status.upper()}"):
            col1, col2 = st.columns(2)
            
            with col1:
                st.metric("执行时间", f"{record.get('execution_time', 0):.2f}秒")
            
            with col2:
                st.metric("执行步数", record.get('step_count', 0))
            
            if record.get("error"):
                st.error(f"错误: {record['error']}")


def clear_screenshots():
    """清理截图目录"""
    screenshots_dir = "screenshots"
    if os.path.exists(screenshots_dir):
        for file in os.listdir(screenshots_dir):
            if file.endswith((".png", ".jpg", ".jpeg")):
                try:
                    os.remove(os.path.join(screenshots_dir, file))
                except:
                    pass


def main():
    """主函数 - 重构版"""
    # 渲染页面头部
    render_header()
    
    # 渲染侧边栏
    config_valid = render_sidebar()
    
    if not config_valid:
        st.error("⚠️ 请先配置正确的系统参数")
        st.info("💡 请检查 .env 文件中的配置项")
        return
    
    # 主内容区域 - 垂直布局
    # 任务输入
    task_config = render_task_input()
    
    # 执行控制
    render_execution_controls(task_config)
    
    # 分隔线
    st.markdown("---")
    
    # 历史记录（可选）
    render_history()


if __name__ == "__main__":
    main()
