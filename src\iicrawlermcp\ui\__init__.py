"""
iICrawlerMCP Streamlit UI 模块

提供图形化用户界面，支持：
- 任务输入和执行
- 实时思考过程显示
- Agent调用过程可视化
- 配置管理和验证
- 执行历史记录
"""

__version__ = "1.0.0"
__author__ = "iICrawlerMCP Team"

# 导出主要组件
from .utils.callbacks import (
    StreamlitCallbackHandler,
    MetricsCallbackHandler,
    DebugCallbackHandler,
)
from .utils.helpers import (
    validate_config,
    load_task_templates,
    save_task_template,
    load_execution_history,
    save_execution_history,
    clear_execution_history,
    format_execution_time,
    get_screenshot_files,
    clean_old_screenshots,
    get_system_info,
    export_execution_data,
)

__all__ = [
    "StreamlitCallbackHandler",
    "MetricsCallbackHandler",
    "DebugCallbackHandler",
    "validate_config",
    "load_task_templates",
    "save_task_template",
    "load_execution_history",
    "save_execution_history",
    "clear_execution_history",
    "format_execution_time",
    "get_screenshot_files",
    "clean_old_screenshots",
    "get_system_info",
    "export_execution_data",
]
