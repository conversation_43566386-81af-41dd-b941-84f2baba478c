"""
UI样式管理模块

提供统一的样式定义和主题管理。
"""

# 主题颜色定义
THEME_COLORS = {
    # 主色调（科技蓝）
    "primary": "#1f77b4",
    "primary_light": "#5da5d8",
    "primary_dark": "#0d5f8a",
    
    # 状态颜色
    "success": "#28a745",
    "warning": "#ffc107",
    "error": "#dc3545",
    "info": "#17a2b8",
    
    # 节点状态颜色
    "node_pending": "#6c757d",
    "node_running": "#007bff",
    "node_completed": "#28a745",
    "node_error": "#dc3545",
    
    # 背景色
    "bg_light": "#f8f9fa",
    "bg_card": "#ffffff",
    "bg_code": "#f1f3f4",
    "bg_dark": "#343a40",
    
    # 边框色
    "border": "#dee2e6",
    "border_active": "#007bff",
    
    # 文本色
    "text_primary": "#212529",
    "text_secondary": "#6c757d",
    "text_light": "#ffffff"
}


def get_custom_css() -> str:
    """
    获取自定义CSS样式
    
    Returns:
        CSS样式字符串
    """
    return f"""
    <style>
    /* 全局样式 */
    .stApp {{
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }}
    
    /* 主标题样式 */
    .main-header {{
        font-size: 2.5rem;
        font-weight: bold;
        background: linear-gradient(135deg, {THEME_COLORS['primary']} 0%, {THEME_COLORS['primary_dark']} 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        text-align: center;
        margin-bottom: 1rem;
        padding: 20px 0;
    }}
    
    .sub-header {{
        font-size: 1.2rem;
        color: {THEME_COLORS['text_secondary']};
        text-align: center;
        margin-bottom: 2rem;
    }}
    
    /* 工作流节点样式 */
    .workflow-node-pending {{
        background: {THEME_COLORS['bg_light']};
        color: {THEME_COLORS['node_pending']};
        border: 2px solid {THEME_COLORS['border']};
        padding: 20px;
        border-radius: 15px;
        text-align: center;
        transition: all 0.3s ease;
        min-height: 140px;
        display: flex;
        flex-direction: column;
        justify-content: center;
    }}
    
    .workflow-node-running {{
        background: linear-gradient(135deg, {THEME_COLORS['primary']} 0%, {THEME_COLORS['primary_light']} 100%);
        color: {THEME_COLORS['text_light']};
        border: 2px solid {THEME_COLORS['primary']};
        padding: 20px;
        border-radius: 15px;
        text-align: center;
        animation: pulse 2s infinite;
        min-height: 140px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        box-shadow: 0 4px 15px rgba(31, 119, 180, 0.3);
    }}
    
    .workflow-node-completed {{
        background: {THEME_COLORS['success']};
        color: {THEME_COLORS['text_light']};
        border: 2px solid {THEME_COLORS['success']};
        padding: 20px;
        border-radius: 15px;
        text-align: center;
        min-height: 140px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        box-shadow: 0 4px 10px rgba(40, 167, 69, 0.2);
    }}
    
    .workflow-node-error {{
        background: {THEME_COLORS['error']};
        color: {THEME_COLORS['text_light']};
        border: 2px solid {THEME_COLORS['error']};
        padding: 20px;
        border-radius: 15px;
        text-align: center;
        min-height: 140px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        box-shadow: 0 4px 10px rgba(220, 53, 69, 0.2);
    }}
    
    /* 脉冲动画 */
    @keyframes pulse {{
        0% {{
            box-shadow: 0 0 0 0 rgba(31, 119, 180, 0.7);
        }}
        70% {{
            box-shadow: 0 0 0 20px rgba(31, 119, 180, 0);
        }}
        100% {{
            box-shadow: 0 0 0 0 rgba(31, 119, 180, 0);
        }}
    }}
    
    /* 工作流箭头样式 */
    .workflow-arrow-pending {{
        color: {THEME_COLORS['node_pending']};
        opacity: 0.5;
    }}
    
    .workflow-arrow-active {{
        color: {THEME_COLORS['primary']};
        animation: flow 1s infinite;
    }}
    
    .workflow-arrow-completed {{
        color: {THEME_COLORS['success']};
    }}
    
    @keyframes flow {{
        0%, 100% {{ transform: translateX(0); }}
        50% {{ transform: translateX(5px); }}
    }}
    
    /* 时间轴样式 */
    .timeline-item {{
        position: relative;
        padding-left: 30px;
        margin-bottom: 15px;
        border-left: 3px solid {THEME_COLORS['border']};
        transition: all 0.3s ease;
    }}
    
    .timeline-item:hover {{
        border-left-color: {THEME_COLORS['primary']};
        background: {THEME_COLORS['bg_light']};
        padding: 10px;
        padding-left: 30px;
        border-radius: 5px;
    }}
    
    .timeline-item::before {{
        content: '';
        position: absolute;
        left: -8px;
        top: 5px;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: {THEME_COLORS['primary']};
        border: 2px solid {THEME_COLORS['bg_card']};
    }}
    
    /* 卡片样式 */
    .custom-card {{
        background: {THEME_COLORS['bg_card']};
        border: 1px solid {THEME_COLORS['border']};
        border-radius: 10px;
        padding: 20px;
        margin: 15px 0;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }}
    
    .custom-card:hover {{
        box-shadow: 0 4px 15px rgba(0,0,0,0.15);
        transform: translateY(-2px);
    }}
    
    /* 状态标签样式 */
    .status-badge {{
        display: inline-block;
        padding: 5px 12px;
        border-radius: 20px;
        font-size: 0.85em;
        font-weight: bold;
        text-transform: uppercase;
    }}
    
    .status-badge-success {{
        background: {THEME_COLORS['success']};
        color: {THEME_COLORS['text_light']};
    }}
    
    .status-badge-error {{
        background: {THEME_COLORS['error']};
        color: {THEME_COLORS['text_light']};
    }}
    
    .status-badge-warning {{
        background: {THEME_COLORS['warning']};
        color: {THEME_COLORS['text_primary']};
    }}
    
    .status-badge-info {{
        background: {THEME_COLORS['info']};
        color: {THEME_COLORS['text_light']};
    }}
    
    /* 思考容器样式 */
    .thinking-container {{
        background: linear-gradient(135deg, {THEME_COLORS['bg_light']} 0%, {THEME_COLORS['bg_card']} 100%);
        border: 1px solid {THEME_COLORS['border']};
        border-radius: 10px;
        padding: 20px;
        margin: 20px 0;
    }}
    
    /* 按钮增强样式 */
    .stButton > button {{
        border-radius: 8px;
        font-weight: 600;
        transition: all 0.3s ease;
    }}
    
    .stButton > button:hover {{
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }}
    
    /* 输入框样式 */
    .stTextInput > div > div > input,
    .stTextArea > div > div > textarea {{
        border-radius: 8px;
        border: 2px solid {THEME_COLORS['border']};
        transition: all 0.3s ease;
    }}
    
    .stTextInput > div > div > input:focus,
    .stTextArea > div > div > textarea:focus {{
        border-color: {THEME_COLORS['primary']};
        box-shadow: 0 0 0 3px rgba(31, 119, 180, 0.1);
    }}
    
    /* 度量值卡片样式 */
    div[data-testid="metric-container"] {{
        background: {THEME_COLORS['bg_card']};
        border: 1px solid {THEME_COLORS['border']};
        border-radius: 10px;
        padding: 15px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.05);
    }}
    
    /* 进度条样式 */
    .stProgress > div > div > div {{
        background: linear-gradient(90deg, {THEME_COLORS['primary']} 0%, {THEME_COLORS['primary_light']} 100%);
    }}
    
    /* Expander样式 */
    .streamlit-expanderHeader {{
        background: {THEME_COLORS['bg_light']};
        border-radius: 8px;
        font-weight: 600;
    }}
    
    /* 选项卡样式 */
    .stTabs [data-baseweb="tab-list"] {{
        gap: 10px;
    }}
    
    .stTabs [data-baseweb="tab"] {{
        border-radius: 8px 8px 0 0;
        font-weight: 600;
    }}
    
    /* 代码块样式 */
    .stCodeBlock {{
        border-radius: 10px;
        border: 1px solid {THEME_COLORS['border']};
    }}
    
    /* 提示信息框样式 */
    .stAlert {{
        border-radius: 10px;
        border-left: 4px solid;
    }}
    
    /* 侧边栏样式 */
    section[data-testid="stSidebar"] {{
        background: linear-gradient(180deg, {THEME_COLORS['bg_light']} 0%, {THEME_COLORS['bg_card']} 100%);
    }}
    
    /* 响应式设计 */
    @media (max-width: 768px) {{
        .main-header {{
            font-size: 2rem;
        }}
        
        .workflow-node-pending,
        .workflow-node-running,
        .workflow-node-completed,
        .workflow-node-error {{
            min-height: 100px;
            padding: 15px;
        }}
    }}
    </style>
    """


def apply_theme():
    """
    应用主题样式到Streamlit应用
    """
    import streamlit as st
    st.markdown(get_custom_css(), unsafe_allow_html=True)