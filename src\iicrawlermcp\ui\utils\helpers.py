"""
Streamlit UI 辅助函数

提供配置验证、模板管理、历史记录等功能
"""

import os
import json
import streamlit as st
from typing import Dict, Any, List, Optional
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def validate_config() -> Dict[str, Any]:
    """
    验证系统配置

    Returns:
        Dict: 包含验证结果的字典
    """
    try:
        from src.iicrawlermcp.core.config import Config

        # 检查必要的配置项
        required_configs = {
            "OPENAI_API_KEY": getattr(Config, "OPENAI_API_KEY", ""),
            "OPENAI_API_BASE": getattr(Config, "OPENAI_API_BASE", ""),
            "OPENAI_MODEL": getattr(Config, "OPENAI_MODEL", ""),
        }

        # 验证API Key
        if not required_configs["OPENAI_API_KEY"]:
            return {"valid": False, "error": "OPENAI_API_KEY 未配置，请检查 .env 文件"}

        # 验证API Key格式
        api_key = required_configs["OPENAI_API_KEY"]
        if len(api_key) < 10:
            return {"valid": False, "error": "OPENAI_API_KEY 格式不正确"}

        # 验证模型名称
        if not required_configs["OPENAI_MODEL"]:
            return {"valid": False, "error": "OPENAI_MODEL 未配置"}

        return {"valid": True, "config": required_configs}

    except ImportError as e:
        return {"valid": False, "error": f"无法导入配置模块: {str(e)}"}
    except Exception as e:
        return {"valid": False, "error": f"配置验证失败: {str(e)}"}


def load_task_templates() -> Dict[str, Dict[str, Any]]:
    """
    加载任务模板

    Returns:
        Dict: 任务模板字典
    """
    templates = {
        "搜索任务": {
            "description": "打开google.com，搜索'迪士尼乐园'，点击第一个非广告结果，然后截图保存",
            "category": "搜索",
            "difficulty": "简单",
        },
        "电商爬取": {
            "description": "访问淘宝首页，搜索'iPhone 15'，查看前3个商品的价格和标题信息",
            "category": "电商",
            "difficulty": "中等",
        },
        "新闻采集": {
            "description": "访问新浪新闻首页，获取今日头条新闻的标题和链接",
            "category": "新闻",
            "difficulty": "中等",
        },
        "表单填写": {
            "description": "访问指定表单页面，填写用户信息并提交",
            "category": "表单",
            "difficulty": "复杂",
        },
        "页面截图": {
            "description": "访问指定网页，等待页面完全加载后进行全页面截图",
            "category": "截图",
            "difficulty": "简单",
        },
        "数据提取": {
            "description": "访问指定页面，提取所有产品信息包括名称、价格、描述等",
            "category": "数据提取",
            "difficulty": "复杂",
        },
    }

    # 尝试从文件加载自定义模板
    try:
        template_file = "ui_templates.json"
        if os.path.exists(template_file):
            with open(template_file, "r", encoding="utf-8") as f:
                custom_templates = json.load(f)
                templates.update(custom_templates)
    except Exception as e:
        logger.warning(f"加载自定义模板失败: {e}")

    return templates


def save_task_template(name: str, template: Dict[str, Any]) -> bool:
    """
    保存任务模板

    Args:
        name: 模板名称
        template: 模板内容

    Returns:
        bool: 保存是否成功
    """
    try:
        template_file = "ui_templates.json"

        # 加载现有模板
        templates = {}
        if os.path.exists(template_file):
            with open(template_file, "r", encoding="utf-8") as f:
                templates = json.load(f)

        # 添加新模板
        templates[name] = template

        # 保存到文件
        with open(template_file, "w", encoding="utf-8") as f:
            json.dump(templates, f, ensure_ascii=False, indent=2)

        return True

    except Exception as e:
        logger.error(f"保存模板失败: {e}")
        return False


def load_execution_history() -> List[Dict[str, Any]]:
    """
    加载执行历史

    Returns:
        List: 执行历史列表
    """
    try:
        history_file = "execution_history.json"
        if os.path.exists(history_file):
            with open(history_file, "r", encoding="utf-8") as f:
                history = json.load(f)
                # 按时间戳排序
                return sorted(history, key=lambda x: x.get("timestamp", ""))
        return []

    except Exception as e:
        logger.error(f"加载执行历史失败: {e}")
        return []


def save_execution_history(record: Dict[str, Any]) -> bool:
    """
    保存执行历史记录

    Args:
        record: 执行记录

    Returns:
        bool: 保存是否成功
    """
    try:
        history_file = "execution_history.json"

        # 加载现有历史
        history = load_execution_history()

        # 添加新记录
        history.append(record)

        # 限制历史记录数量（保留最近100条）
        if len(history) > 100:
            history = history[-100:]

        # 保存到文件
        with open(history_file, "w", encoding="utf-8") as f:
            json.dump(history, f, ensure_ascii=False, indent=2)

        return True

    except Exception as e:
        logger.error(f"保存执行历史失败: {e}")
        return False


def clear_execution_history() -> bool:
    """
    清空执行历史

    Returns:
        bool: 清空是否成功
    """
    try:
        history_file = "execution_history.json"
        if os.path.exists(history_file):
            os.remove(history_file)
        return True

    except Exception as e:
        logger.error(f"清空执行历史失败: {e}")
        return False


def format_execution_time(seconds: float) -> str:
    """
    格式化执行时间

    Args:
        seconds: 秒数

    Returns:
        str: 格式化的时间字符串
    """
    if seconds < 60:
        return f"{seconds:.2f}秒"
    elif seconds < 3600:
        minutes = int(seconds // 60)
        remaining_seconds = seconds % 60
        return f"{minutes}分{remaining_seconds:.1f}秒"
    else:
        hours = int(seconds // 3600)
        remaining_minutes = int((seconds % 3600) // 60)
        return f"{hours}小时{remaining_minutes}分钟"


def get_screenshot_files() -> List[str]:
    """
    获取截图文件列表

    Returns:
        List: 截图文件路径列表
    """
    try:
        screenshots_dir = "screenshots"
        if not os.path.exists(screenshots_dir):
            return []

        # 获取所有图片文件
        image_extensions = (".png", ".jpg", ".jpeg", ".gif", ".bmp")
        screenshot_files = []

        for file in os.listdir(screenshots_dir):
            if file.lower().endswith(image_extensions):
                file_path = os.path.join(screenshots_dir, file)
                screenshot_files.append(file_path)

        # 按修改时间排序（最新的在前）
        screenshot_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)

        return screenshot_files

    except Exception as e:
        logger.error(f"获取截图文件失败: {e}")
        return []


def clean_old_screenshots(keep_count: int = 20) -> int:
    """
    清理旧的截图文件

    Args:
        keep_count: 保留的文件数量

    Returns:
        int: 删除的文件数量
    """
    try:
        screenshot_files = get_screenshot_files()

        if len(screenshot_files) <= keep_count:
            return 0

        # 删除多余的文件
        files_to_delete = screenshot_files[keep_count:]
        deleted_count = 0

        for file_path in files_to_delete:
            try:
                os.remove(file_path)
                deleted_count += 1
            except Exception as e:
                logger.warning(f"删除文件失败 {file_path}: {e}")

        return deleted_count

    except Exception as e:
        logger.error(f"清理截图文件失败: {e}")
        return 0


@st.cache_data
def get_system_info() -> Dict[str, Any]:
    """
    获取系统信息（缓存）

    Returns:
        Dict: 系统信息
    """
    import platform
    import psutil

    try:
        return {
            "platform": platform.platform(),
            "python_version": platform.python_version(),
            "cpu_count": psutil.cpu_count(),
            "memory_total": psutil.virtual_memory().total // (1024**3),  # GB
            "disk_free": psutil.disk_usage(".").free // (1024**3),  # GB
        }
    except Exception as e:
        logger.error(f"获取系统信息失败: {e}")
        return {}


def export_execution_data(format_type: str = "json") -> Optional[str]:
    """
    导出执行数据

    Args:
        format_type: 导出格式 ("json", "csv")

    Returns:
        str: 导出文件路径，失败返回None
    """
    try:
        history = load_execution_history()
        if not history:
            return None

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        if format_type == "json":
            filename = f"execution_export_{timestamp}.json"
            with open(filename, "w", encoding="utf-8") as f:
                json.dump(history, f, ensure_ascii=False, indent=2)
            return filename

        elif format_type == "csv":
            import pandas as pd

            filename = f"execution_export_{timestamp}.csv"
            df = pd.DataFrame(history)
            df.to_csv(filename, index=False, encoding="utf-8-sig")
            return filename

        return None

    except Exception as e:
        logger.error(f"导出数据失败: {e}")
        return None
