"""
测试LangGraph迁移

测试新的LangGraph架构是否正常工作，特别是上下文管理和Agent复用。
"""

import os
import sys
import logging
from dotenv import load_dotenv

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# 加载环境变量
load_dotenv()

# 配置日志和编码
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_langgraph_execution():
    """测试LangGraph执行流程"""
    print("\n" + "="*60)
    print("测试 LangGraph 执行流程")
    print("="*60)
    
    try:
        from studio.graph_adapter import graph
        from src.iicrawlermcp.core.graph_state import create_initial_state

        # 测试任务
        task = "导航到booking.com,然后截图"

        print(f"\n任务: {task}")
        print("-"*40)

        # 执行任务
        initial_state = create_initial_state(task)
        result = graph.invoke(initial_state)
        
        print("\n执行结果:")
        print("-"*40)
        print(f"状态: {result.get('final_status')}")
        print(f"是否完成: {result.get('is_complete')}")
        
        if result.get('plan'):
            print(f"\n执行计划: {result.get('plan')}")
        
        if result.get('execution_results'):
            print(f"\n执行步骤数: {len(result['execution_results'])}")
            for i, step in enumerate(result['execution_results'], 1):
                print(f"  步骤{i}: {step.get('agent')} - 已执行")
        
        if result.get('error'):
            print(f"\n错误: {result['error']}")
            
        return result.get('is_complete', False)
        
    except ImportError as e:
        print(f"❌ LangGraph未安装: {e}")
        print("请运行: uv pip install langgraph")
        return False
    except Exception as e:
        print(f"ERROR 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_agent_reuse():
    """测试Agent实例复用"""
    print("\n" + "="*60)
    print("测试 Agent 实例复用")
    print("="*60)
    
    try:
        from iicrawlermcp.core.agent_adapter import get_or_create_agent_node
        
        # 获取两次相同的Agent节点
        browser_node1 = get_or_create_agent_node("browser")
        browser_node2 = get_or_create_agent_node("browser")
        
        # 验证是同一个实例
        is_same = browser_node1 is browser_node2
        print(f"\nBrowser节点复用测试: {'PASS' if is_same else 'FAIL'}")
        print(f"  节点1 ID: {id(browser_node1)}")
        print(f"  节点2 ID: {id(browser_node2)}")
        print(f"  是否相同: {is_same}")
        
        # 测试不同Agent
        element_node = get_or_create_agent_node("element")
        is_different = browser_node1 is not element_node
        print(f"\n不同Agent测试: {'PASS' if is_different else 'FAIL'}")
        print(f"  Browser ID: {id(browser_node1)}")
        print(f"  Element ID: {id(element_node)}")
        print(f"  是否不同: {is_different}")
        
        return is_same and is_different
        
    except Exception as e:
        print(f"ERROR 测试失败: {e}")
        return False


def test_delegation_tools():
    """测试委托工具的LangGraph模式"""
    print("\n" + "="*60)
    print("测试委托工具 LangGraph 模式")
    print("="*60)
    
    try:
        from iicrawlermcp.tools import delegation_tools
        
        # 检查是否启用了LangGraph
        if delegation_tools.USE_LANGGRAPH:
            print("PASS LangGraph模式已启用")
            
            # 测试browser action finder
            print("\n测试 smart_browser_action_finder:")
            result = delegation_tools.smart_browser_action_finder.invoke(
                {"action_description": "获取当前页面URL"}
            )
            print(f"  结果: {result[:100]}...")
            
            return True
        else:
            print("WARN LangGraph模式未启用，使用旧模式")
            return False
            
    except Exception as e:
        print(f"ERROR 测试失败: {e}")
        return False


def test_state_management():
    """测试状态管理"""
    print("\n" + "="*60)
    print("测试状态管理")
    print("="*60)
    
    try:
        from iicrawlermcp.core.graph_state import create_initial_state, extract_agent_context
        
        # 创建初始状态
        state = create_initial_state("测试任务")
        print(f"\n初始状态创建: ✅")
        print(f"  消息数: {len(state['messages'])}")
        print(f"  活跃Agent: {state['active_agent']}")
        print(f"  是否完成: {state['is_complete']}")
        
        # 测试上下文提取
        browser_context = extract_agent_context(state, "browser")
        print(f"\nBrowser上下文提取: ✅")
        print(f"  包含键: {list(browser_context.keys())}")
        
        element_context = extract_agent_context(state, "element")
        print(f"\nElement上下文提取: ✅")
        print(f"  包含键: {list(element_context.keys())}")
        
        return True
        
    except Exception as e:
        print(f"ERROR 测试失败: {e}")
        return False


def main():
    """运行所有测试"""
    print("\n" + "="*60)
    print("LangGraph 迁移测试套件")
    print("="*60)
    
    tests = [
        # ("状态管理", test_state_management),
        # ("Agent复用", test_agent_reuse),
        # ("委托工具", test_delegation_tools),
        ("执行流程", test_langgraph_execution),
    ]
    
    results = []
    for name, test_func in tests:
        try:
            success = test_func()
            results.append((name, success))
        except Exception as e:
            print(f"\n❌ {name}测试异常: {e}")
            results.append((name, False))
    
    # 汇总结果
    print("\n" + "="*60)
    print("测试结果汇总")
    print("="*60)
    
    for name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{name}: {status}")
    
    total = len(results)
    passed = sum(1 for _, s in results if s)
    print(f"\n总计: {passed}/{total} 通过")
    
    if passed == total:
        print("\n🎉 所有测试通过！LangGraph迁移成功！")
    else:
        print("\n⚠️ 部分测试失败，请检查错误信息")


if __name__ == "__main__":
    main()