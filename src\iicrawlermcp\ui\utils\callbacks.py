"""
Streamlit 回调处理器

实现 LangChain Agent 执行过程的实时可视化显示
"""

import streamlit as st
from typing import Dict, Any, List, Optional
from langchain.callbacks.base import BaseCallbackHandler
from langchain.schema import AgentAction, AgentFinish, LLMResult
import time
import json


class StreamlitCallbackHandler(BaseCallbackHandler):
    """Streamlit回调处理器，用于显示Agent思考过程"""

    def __init__(self, container):
        """
        初始化回调处理器

        Args:
            container: Streamlit容器，用于显示内容
        """
        self.container = container
        self.step_count = 0
        self.current_step_container = None
        self.start_time = time.time()
        self.step_times = []

    def on_llm_start(
        self, serialized: Dict[str, Any], prompts: List[str], **kwargs: Any
    ) -> None:
        """LLM开始推理时触发"""
        with self.container.container():
            st.write("🤔 **AI正在思考...**")

    def on_llm_end(self, response: LLMResult, **kwargs: Any) -> None:
        """LLM推理结束时触发"""
        pass

    def on_llm_error(self, error: Exception, **kwargs: Any) -> None:
        """LLM推理出错时触发"""
        with self.container.container():
            st.error(f"🚨 **LLM推理错误**: {str(error)}")

    def on_agent_action(self, action: AgentAction, **kwargs: Any) -> None:
        """Agent执行动作时触发"""
        self.step_count += 1
        step_start_time = time.time()

        with self.container.container():
            # 创建可折叠的步骤显示
            with st.expander(
                f"🔧 步骤 {self.step_count}: {action.tool}", expanded=True
            ):

                # 工具信息
                col1, col2 = st.columns([1, 3])
                with col1:
                    st.write("**工具名称**")
                    st.code(action.tool)

                with col2:
                    st.write("**工具输入**")
                    # 格式化显示工具输入
                    if isinstance(action.tool_input, dict):
                        st.json(action.tool_input)
                    else:
                        st.code(str(action.tool_input))

                # 思考过程
                if hasattr(action, "log") and action.log:
                    st.write("**思考过程**")
                    # 清理和格式化思考过程
                    cleaned_log = self._clean_thought_process(action.log)
                    st.text_area(
                        "思考过程",
                        cleaned_log,
                        height=100,
                        key=f"thought_{self.step_count}",
                        label_visibility="collapsed",
                    )

                # 执行状态
                st.info(f"⏳ 正在执行工具: {action.tool}")

        self.step_times.append(step_start_time)

    def on_tool_start(
        self, serialized: Dict[str, Any], input_str: str, **kwargs: Any
    ) -> None:
        """工具开始执行时触发"""
        tool_name = serialized.get("name", "未知工具")
        with self.container.container():
            st.write(f"🛠️ **开始执行工具**: {tool_name}")

    def on_tool_end(self, output: str, **kwargs: Any) -> None:
        """工具执行结束时触发"""
        step_end_time = time.time()

        with self.container.container():
            # 显示工具输出
            with st.expander(f"📤 步骤 {self.step_count} 输出", expanded=False):
                # 限制输出长度，避免界面过于冗长
                if len(output) > 500:
                    st.text_area(
                        "工具输出",
                        output[:500] + "...",
                        height=100,
                        label_visibility="collapsed",
                    )
                    with st.expander("查看完整输出"):
                        st.text(output)
                else:
                    st.text_area(
                        "工具输出", output, height=100, label_visibility="collapsed"
                    )

            # 显示执行时间
            if self.step_times:
                execution_time = step_end_time - self.step_times[-1]
                st.success(
                    f"✅ **步骤 {self.step_count} 完成** (耗时: {execution_time:.2f}秒)"
                )

    def on_tool_error(self, error: Exception, **kwargs: Any) -> None:
        """工具执行出错时触发"""
        with self.container.container():
            st.error(f"🚨 **工具执行错误**: {str(error)}")

            # 显示错误详情
            with st.expander("错误详情"):
                st.code(str(error))

    def on_agent_finish(self, finish: AgentFinish, **kwargs: Any) -> None:
        """Agent完成任务时触发"""
        total_time = time.time() - self.start_time

        with self.container.container():
            st.success(
                f"🎉 **任务完成！** 总耗时: {total_time:.2f}秒，共执行 {self.step_count} 个步骤"
            )

            # 显示最终输出
            if hasattr(finish, "return_values") and finish.return_values:
                with st.expander("📋 最终结果", expanded=True):
                    if isinstance(finish.return_values, dict):
                        for key, value in finish.return_values.items():
                            st.write(f"**{key}**: {value}")
                    else:
                        st.write(str(finish.return_values))

    def on_text(self, text: str, **kwargs: Any) -> None:
        """处理文本输出"""
        # 过滤掉一些不必要的文本输出
        if (
            text.strip()
            and not text.startswith("Entering new")
            and not text.startswith("Finished")
        ):
            with self.container.container():
                st.write(f"💭 {text}")

    def _clean_thought_process(self, log: str) -> str:
        """清理和格式化思考过程文本"""
        # 移除一些不必要的前缀和格式
        cleaned = (
            log.replace("Thought: ", "")
            .replace("Action: ", "")
            .replace("Action Input: ", "")
        )

        # 移除过长的重复内容
        lines = cleaned.split("\n")
        cleaned_lines = []
        for line in lines:
            line = line.strip()
            if line and len(line) < 200:  # 过滤过长的行
                cleaned_lines.append(line)

        return "\n".join(cleaned_lines[:10])  # 限制行数


class MetricsCallbackHandler(BaseCallbackHandler):
    """指标收集回调处理器"""

    def __init__(self):
        self.metrics = {
            "total_steps": 0,
            "total_time": 0,
            "tool_usage": {},
            "error_count": 0,
            "llm_calls": 0,
        }
        self.start_time = None

    def on_agent_action(self, action: AgentAction, **kwargs: Any) -> None:
        """收集Agent动作指标"""
        self.metrics["total_steps"] += 1
        tool_name = action.tool
        self.metrics["tool_usage"][tool_name] = (
            self.metrics["tool_usage"].get(tool_name, 0) + 1
        )

    def on_llm_start(
        self, serialized: Dict[str, Any], prompts: List[str], **kwargs: Any
    ) -> None:
        """收集LLM调用指标"""
        self.metrics["llm_calls"] += 1
        if self.start_time is None:
            self.start_time = time.time()

    def on_tool_error(self, error: Exception, **kwargs: Any) -> None:
        """收集错误指标"""
        self.metrics["error_count"] += 1

    def on_agent_finish(self, finish: AgentFinish, **kwargs: Any) -> None:
        """完成指标收集"""
        if self.start_time:
            self.metrics["total_time"] = time.time() - self.start_time

    def get_metrics(self) -> Dict[str, Any]:
        """获取收集的指标"""
        return self.metrics.copy()


class DebugCallbackHandler(BaseCallbackHandler):
    """调试回调处理器，用于开发和调试"""

    def __init__(self, debug_container):
        self.debug_container = debug_container
        self.events = []

    def on_llm_start(
        self, serialized: Dict[str, Any], prompts: List[str], **kwargs: Any
    ) -> None:
        """记录LLM开始事件"""
        event = {
            "type": "llm_start",
            "timestamp": time.time(),
            "data": {"prompts": prompts[:1]},  # 只记录第一个prompt
        }
        self.events.append(event)
        self._update_debug_display()

    def on_agent_action(self, action: AgentAction, **kwargs: Any) -> None:
        """记录Agent动作事件"""
        event = {
            "type": "agent_action",
            "timestamp": time.time(),
            "data": {
                "tool": action.tool,
                "input": (
                    str(action.tool_input)[:100] + "..."
                    if len(str(action.tool_input)) > 100
                    else str(action.tool_input)
                ),
            },
        }
        self.events.append(event)
        self._update_debug_display()

    def on_tool_end(self, output: str, **kwargs: Any) -> None:
        """记录工具结束事件"""
        event = {
            "type": "tool_end",
            "timestamp": time.time(),
            "data": {"output_length": len(output)},
        }
        self.events.append(event)
        self._update_debug_display()

    def _update_debug_display(self):
        """更新调试显示"""
        with self.debug_container:
            st.subheader("🐛 调试信息")

            # 显示最近的事件
            recent_events = self.events[-5:]  # 显示最近5个事件
            for i, event in enumerate(recent_events):
                with st.expander(
                    f"事件 {len(self.events) - len(recent_events) + i + 1}: {event['type']}"
                ):
                    st.json(event)
