# CLAUDE.md

此文件为 Claude Code (claude.ai/code) 在此代码库中工作时提供指导。

## 项目概述

iICrawlerMCP 是一个基于 LangGraph 和 Playwright 构建的智能网页爬虫系统，支持 Model Context Protocol (MCP)。它采用统一WebAgent架构和智能工作流编排，通过LangGraph状态管理实现复杂的网页自动化任务。

**当前版本 (v1.1)**：基于LangGraph的智能工作流系统  
**架构特点**：统一WebAgent + LangGraph编排 + 全局搜索能力 + 智能代码生成

## 核心架构

### 智能Agent系统（当前 v1.1）
- **WebAgent** (`src/iicrawlermcp/agents/web_agent.py`)：统一Web自动化Agent，集成DOM分析、浏览器操作和搜索功能 (13个工具)
- **SmartPlannerAgent** (`src/iicrawlermcp/agents/smart_planner_agent.py`)：统一智能规划Agent，整合了任务规划和提示词优化功能 (4个工具)
- **CodeGenAgent** (`src/iicrawlermcp/agents/codegen_agent.py`)：代码生成专家，将操作历史转换为Playwright代码 (3个工具)

> ⚠️ **注意**：CrawlerAgent（主协调）和RecordAgent（操作录制）功能已集成到LangGraph工作流和其他Agent中

### 核心组件
- **BrowserToolkit** (`src/iicrawlermcp/tools/browser_tools.py`)：统一浏览器工具包（14个工具）
- **DOM Tools** (`src/iicrawlermcp/tools/dom_tools.py`)：精确DOM提取工具包（12个智能工具）
- **Prompt Tools** (`src/iicrawlermcp/tools/prompt_tools.py`)：提示词优化工具（4个工具）
- **Planning Tools** (`src/iicrawlermcp/tools/planning_tools.py`)：任务规划和分解工具（3个工具）
- **Validation Tools** (`src/iicrawlermcp/tools/validation_tools.py`)：结果验证和质量评估工具（4个工具）
- **Search Tools** (`src/iicrawlermcp/tools/search_tools.py`)：网络搜索工具（1个工具，集成到多个Agent）
- **LangGraph Studio** (`studio/graph_adapter.py`)：可视化工作流调试和执行
- **MCP Server** (`src/iicrawlermcp/mcp/server.py`)：Model Context Protocol集成，外部系统接入

**工具总计**：38个专业工具

## 开发命令

### 环境管理（使用uv）

本项目使用 [uv](https://github.com/astral-sh/uv) 作为Python包和环境管理工具。uv是一个极快的Python包管理器，由Rust编写。

### 快速开始（推荐）
```bash
# 1. 克隆项目后，进入项目目录
cd iICrawlerMCP

# 2. 创建并激活虚拟环境（一条命令）
uv venv && .venv\Scripts\activate.bat

# 3. 安装所有依赖（包括开发依赖）
uv pip install -e ".[dev]"

# 4. 安装Playwright浏览器
playwright install

# 5. 运行测试确认环境正常
uv run pytest tests/unit/test_browser.py
```

### 详细安装和设置
```bash
# 创建虚拟环境
uv venv

# 激活虚拟环境 (Windows CMD)
.venv\Scripts\activate.bat

# 激活虚拟环境 (Windows PowerShell)
.venv\Scripts\Activate.ps1

# 安装项目依赖
uv pip install -e .

# 安装开发依赖
uv pip install -e ".[dev]"

# 安装Playwright浏览器
playwright install

# 验证安装
python -c "from iicrawlermcp.agents import build_agent; print('✅ 安装成功!')"
```

### 依赖管理
```bash
# 安装单个包
uv pip install package-name

# 升级包
uv pip install --upgrade package-name

# 查看已安装的包
uv pip list

# 生成requirements.txt
uv pip freeze > requirements.txt

# 同步依赖（基于pyproject.toml）
uv pip sync

# 编译依赖锁文件（推荐用于生产环境）
uv pip compile pyproject.toml -o requirements.lock
```

### uv特性和优势
- **极速安装**：比pip快10-100倍，使用Rust实现
- **内存高效**：全局缓存，避免重复下载
- **兼容性好**：完全兼容pip命令和pyproject.toml
- **并行处理**：自动并行下载和安装包
- **智能解析**：更快的依赖解析算法

### 测试
```bash
# 确保在激活的虚拟环境中运行测试
# 运行所有测试
uv run pytest

# 运行覆盖率测试
uv run pytest --cov=src/iicrawlermcp

# 运行特定测试类别
uv run pytest tests/unit/          # 单元测试
uv run pytest tests/integration/   # 集成测试
uv run pytest tests/e2e/          # 端到端测试

# 运行单个测试文件
uv run pytest tests/integration/test_connection.py
```

### 代码质量检查
```bash
# 格式化代码
uv run black src/ tests/

# 代码检查
uv run flake8 src/ tests/

# 类型检查
uv run mypy src/

# 排序导入
uv run isort src/ tests/
```

### 运行应用
```bash
# LangGraph Studio（推荐可视化调试）
python run_studio.py

# MCP服务器
uv run python src/iicrawlermcp/mcp/run_server.py sse --host 127.0.0.1 --port 8000

# 直接Python使用
uv run python examples/basic/browser_basics.py
uv run python examples/basic/agent_basics.py
uv run python examples/advanced/e2e_multi_agent_demo.py
```

## 配置

在项目根目录创建 `.env` 文件：
```env
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4-turbo
HEADLESS=true
VERBOSE=true
DEFAULT_SCREENSHOT_PATH=screenshots/
```

## Agent使用模式

### 主Agent（推荐）
```python
from iicrawlermcp.agents import build_agent
agent = build_agent()
result = agent.invoke("导航到 google.com 并搜索'迪士尼世界'")
```

### 专业化Agent
```python
from iicrawlermcp.agents import build_web_agent, build_smart_planner_agent, build_codegen_agent

# 统一Web自动化Agent（集成搜索功能）
web_agent = build_web_agent()
web_agent.invoke("导航到 https://example.com，分析页面结构并截图")

# 智能规划Agent（整合了任务规划和提示词优化）
smart_planner = build_smart_planner_agent()
smart_planner.invoke("为爬取电商网站商品信息制定详细计划")

# 代码生成Agent
codegen_agent = build_codegen_agent()
codegen_agent.invoke("将操作历史转换为Playwright自动化代码")
```

### 增强的搜索功能
所有主要Agent现在都集成了Google搜索能力：
- **SmartPlannerAgent**: 搜索技术文档、最佳实践、解决方案来生成更准确的执行计划
- **WebAgent**: 搜索网站域名、技术信息、API文档来辅助网页自动化任务

```python
# 搜索功能示例
smart_planner.invoke("搜索Selenium和Playwright的对比，然后制定网页自动化方案")
web_agent.invoke("搜索淘宝网的域名，然后导航并分析页面结构")
```

### LangGraph Studio使用
```bash
# 启动 LangGraph Studio
python run_studio.py

# 在浏览器中访问
# https://smith.langchain.com/studio/?baseUrl=http://127.0.0.1:2024

# 在Studio界面中输入任务，例如：
# "打开booking.com搜索上海的酒店"
```

## LangGraph工作流系统

### 工作流架构
项目基于LangGraph实现了智能工作流管理，支持状态驱动的任务执行：

#### 核心组件
- **Graph Adapter** (`src/studio/graph_adapter.py`)：LangGraph Studio适配器，工作流定义和执行引擎
- **GraphState** (`src/iicrawlermcp/core/graph_state.py`)：统一状态模型，管理任务执行上下文
- **Agent Adapter** (`src/iicrawlermcp/core/agent_adapter.py`)：Agent适配器，将传统 Agent 集成到 LangGraph 中

#### 工作流节点
1. **Smart Planner Node**：智能任务规划和分解
2. **Web Node**：Web自动化执行（集成浏览器和DOM操作）
3. **CodeGen Node**：代码生成和输出

#### 智能路由机制
```python
def should_continue(state: CrawlerState) -> Literal["continue", "end"]:
    """智能判断是否继续执行工作流"""
    if state.get("is_complete", False):
        return "end"
    if state.get("error") and state.get("retry_count", 0) >= 3:
        return "end"
    return "continue"
```

### 状态管理
LangGraph通过统一的状态模型管理复杂任务执行：

```python
class CrawlerState(TypedDict):
    """工作流状态定义"""
    messages: List[BaseMessage]      # 对话历史
    current_url: Optional[str]       # 当前URL
    dom_state: Optional[Dict]        # DOM缓存
    task_plan: Optional[Dict]        # 任务计划
    active_agent: str                # 活跃Agent
    execution_results: List[Dict]    # 执行结果
    context_variables: Dict          # 共享上下文
    error: Optional[str]             # 错误信息
    retry_count: int                 # 重试次数
    is_complete: bool                # 完成状态
```

### 执行策略
- **条件路由**：基于执行结果和验证状态智能选择下一步
- **错误恢复**：自动重试机制，最多3次重试
- **状态持久化**：支持长时间运行的复杂任务
- **并行执行**：支持多个独立任务并行处理

## 关键文件和目录

- `src/iicrawlermcp/core/browser.py`：全局浏览器管理
- `src/studio/graph_adapter.py`：LangGraph Studio工作流适配器
- `src/iicrawlermcp/core/graph_state.py`：统一状态管理
- `src/iicrawlermcp/dom/core/extractor.py`：DOM提取引擎
- `examples/`：使用示例和演示
- `tests/`：测试文件（当前仅包含少量演示文件）
- `screenshots/`：默认截图输出目录

> ⚠️ **注意**：`docs/`目录和`codegen/`目录尚未实现

## 测试指南

当前测试文件：
- `tests/langgraph_migration.py` - LangGraph迁移测试
- `tests/search_demo.py` - 搜索功能演示
- `tests/test_prompt_optimization.py` - 提示词优化测试

> ⚠️ **待完善**：需要建立完整的测试体系（unit/integration/e2e）

## MCP集成

项目完整支持 Model Context Protocol (MCP) 以供外部系统集成：
- **MCP服务器**：`src/iicrawlermcp/mcp/server.py` - 核心服务器实现
- **启动脚本**：`src/iicrawlermcp/mcp/run_server.py` - 服务器启动工具
- **客户端示例**：`examples/mcp/call_mcp.py` - 调用示例
- **支持协议**：SSE (Server-Sent Events) 和 HTTP 传输
- **集成方式**：Claude Desktop、Claude Code等MCP兼容客户端

## 提示工程原则

在此项目中与 Claude Code 交互时，请遵循以下核心提示工程原则：

### 1. 定义角色和任务
<role_definition>
- 为 Claude 分配与任务相关的特定专家角色
- 明确定义预期结果和边界
- 示例："你是一名专业的Python开发者，专长于网页自动化和多Agent系统"
</role_definition>

### 2. 使用结构化分隔符
<structural_guidelines>
- 使用 XML 标签（`<document>`、`<example>`、`<code>`）或 Markdown 来组织信息
- 用清晰的分隔符包装特定信息块
- 这有助于 Claude 更好地理解和引用特定部分
</structural_guidelines>

### 3. 提供分步指令
<step_by_step_approach>
- 将复杂任务分解为有序的、逻辑性的步骤列表
- 指令顺序对准确性至关重要
- 镜像人类专家分析工作流
- 示例工作流：
  1. 首先分析现有代码结构
  2. 然后识别特定修改点  
  3. 最后按照项目模式实施更改
</step_by_step_approach>

### 4. 提供优质示例
<example_standards>
- 包含高质量的示范案例
- 显示预期的输入/输出格式
- 引导 Claude 达到所需的质量标准
</example_standards>

### 5. 指定输出格式
<output_format>
- 用特定的 XML 标签包装最终结果以便解析
- 示例：`<final_result>` 和 `</final_result>`
- 便于下游应用处理
</output_format>

### 6. 预填充响应（高级）
<response_prefill>
- 在需要时提供响应结构的开头
- 对强制特定输出格式（JSON等）有效
- 引导 Claude 产生结构化响应
</response_prefill>

## 用户规则和开发标准

<user_rules>
**关键要求**：作为专业的 iICrawlerMCP 开发者，您必须遵循这些项目规则：

### 🔒 核心治理规则（不可违反）

#### 规则4：代码修改授权 **[最高优先级]**
<modification_authorization>
**要求**：仅在用户明确请求时才修改代码
**执行原则**：
- 优先提供分析和建议
- 在修改前获得明确的用户授权
- 代码审查和问题诊断 ≠ 修改授权
**豁免权限**：此规则可豁免其他所有规则的执行
</modification_authorization>

#### 规则9：代码质量标准
<code_quality>
**要求**：所有代码修改必须通过质量检查
**检查标准**：
- 格式化：`black src/ tests/`
- 类型检查：`mypy src/`
- 代码检查：`flake8 src/ tests/`
**测试要求**：
- 新功能必须包含单元测试
- 重要修改需要集成测试
- 测试覆盖率不低于80%
</code_quality>

#### 规则10：项目架构约束
<architecture_constraints>
**要求**：修改必须遵循项目架构模式
**架构层次**：
- **Agent层** (`agents/`)：智能代理逻辑
- **工具层** (`tools/`)：功能工具实现
- **核心层** (`core/`)：基础设施
- **DOM层** (`dom/`)：DOM处理专用
**修改原则**：
- 不破坏现有分层结构
- 新功能按层次归类
- 保持高内聚低耦合
</architecture_constraints>

### ⚙️ 流程执行规则（可配置）

#### 规则2：工作流驱动开发
<workflow_requirements>
**要求**：根据任务复杂度选择合适的Agent工作流
**复杂度分级**：
- **简单任务**(<50行代码)：直接开发，无需多Agent
- **中等任务**(50-200行)：使用1-2个专业Agent
- **复杂任务**(>200行)：多Agent协作工作流
**Agent选择优先级**：
1. **项目内Agent**：`CrawlerAgent`、`WebAgent`、`PromptOptimizationAgent`、`PlannerAgent`、`ValidatorAgent`、`RecordAgent`
2. **配置Agent**：`python-pro`、`code-reviewer`、`test-automator`等56个专业Agent（位置：`D:\Users\wenbochen.CN1\.claude\agents\`）
**工作流标准**：
- 记录Agent调用链和决策过程
- 优先使用自动委托，必要时显式调用特定Agent
</workflow_requirements>

#### 规则3：版本控制要求
<git_standards>
**要求**：重要修改必须提交到git
**提交标准**：
- 功能完成后立即提交
- 清晰、描述性的提交信息
- 提交前通过测试
- 提交中包含文档更新
</git_standards>

#### 规则6：网络研究要求
<network_research>
**要求**：架构讨论必须包含当前的网络研究
**研究范围**：
- 最新的技术标准和最佳实践
- 相关开源项目发展动态
- 框架和库版本信息
- 行业标准和规范
**执行标准**：
- 基于最新信息提供建议
- 引用可靠的技术资源
- 考虑技术发展趋势
</network_research>

### 📝 输出标准规则（可调整）

#### 规则1：文档同步要求
<documentation_sync>
**要求**：代码修改需要相应的文档更新
**分级标准**：
- **重要修改**（架构变更）：必须同步更新文档
- **功能修改**（新增功能）：可延后到里程碑节点更新
- **修复性修改**（Bug修复）：可只更新changelog
**目标文档**：
- `README.md` - 功能变更、架构调整
- `CLAUDE.md` - 开发指导更新
- `docs/` 目录 - 相关文档
- 代码注释和文档字符串
</documentation_sync>

#### 规则7：中文语言要求
<chinese_language>
**要求**：所有文本内容必须使用中文，专业术语除外
**语言标准**：
- 文档、注释和说明使用中文
- 技术术语（API、JSON、XML等）保持英文
- 代码变量名和函数名遵循英文惯例
- 错误信息和日志尽可能使用中文
</chinese_language>

#### 规则8：测试管理要求
<testing_requirements>
**要求**：测试管理和执行遵循项目标准
**执行原则**：
- **禁止生成测试文件**：Claude不得创建任何测试脚本文件
- **指导测试方法**：仅提供测试方法和命令指导
- **用户自主测试**：所有测试执行由用户完成
**测试目录结构**：
- `tests/unit/` - 单元测试（保留）
- `tests/integration/` - 集成测试（保留）
- `tests/e2e/` - 端到端测试（保留）
**管理标准**：
- **正式测试**：由开发者编写并提交到版本控制
- **临时测试**：用户自行创建和管理
- **测试指导**：提供测试步骤和命令，不创建测试文件
</testing_requirements>

### 🖥️ 环境适配规则（环境相关）

#### 规则5：Windows CMD兼容性
<windows_environment>
**要求**：这是Windows CMD命令行环境
**命令要求**：
- 使用Windows路径格式（`\` 分隔符）
- 注意PowerShell与CMD差异
- 用双引号包围文件路径
- 使用Windows兼容的命令语法
**uv使用注意**：
- 激活虚拟环境：CMD使用`.venv\Scripts\activate.bat`
- PowerShell使用`.venv\Scripts\Activate.ps1`
- uv命令在Windows下完全兼容，无需特殊配置
</windows_environment>

### 📊 规则优先级矩阵
<priority_matrix>
**规则优先级定义**：
1. **最高优先级**：规则4（用户授权）- 控制是否执行
2. **系统级**：规则5（Windows兼容）、规则9（代码质量）、规则10（架构约束）- 执行约束
3. **流程级**：规则2（工作流）、规则6（网络研究）- 执行策略
4. **输出级**：规则1（文档同步）、规则7（中文）、规则8（测试）- 输出约束
5. **最终级**：规则3（版本控制）- 最终确认

**冲突解决**：高优先级规则可豁免低优先级规则
</priority_matrix>

### 🎯 项目架构适配信息
<project_specific>
**当前版本(v1.1)**：统一WebAgent + LangGraph工作流系统
- **核心Agent（4个实现）**：WebAgent（统一Web自动化）、PlannerAgent（任务规划）、PromptOptimizationAgent（提示优化）、ValidatorAgent（结果验证）
- **工具生态（37个工具）**：
  - BrowserToolkit（14个工具）：浏览器操作和页面交互
  - DOM Tools（11个工具）：元素发现和数据提取
  - Prompt Tools（4个工具）：提示词优化和模板生成
  - Planning Tools（3个工具）：任务规划和分解
  - Validation Tools（4个工具）：结果验证和质量评估
  - Search Tools（1个工具）：网络搜索功能
- **核心引擎**：Browser（Playwright封装）、LangGraph Orchestrator（工作流编排）、DOMExtractor（元素提取）
- **MCP支持**：完整的MCP服务器实现，支持外部系统集成

**已实现能力**：统一Web自动化 + LangGraph编排
- **统一架构**：WebAgent集成了浏览器操作和DOM分析能力
- **工作流编排**：基于LangGraph的智能状态管理
- **智能路由**：条件判断和循环检测机制

**技术栈**：
- **核心框架**：Python 3.11+、LangGraph、LangChain、Playwright、MCP v1.12+
- **包管理工具**：uv（超快速Python包管理器，Rust编写）
- **用户界面**：Streamlit WebUI、命令行工具、MCP集成
- **开发工具**：pytest（测试）、black（格式化）、mypy（类型检查）、flake8（代码检查）
- **部署环境**：Windows环境优先，支持跨平台部署
</project_specific>

### 🚦 合规执行
<compliance>
违反这些规则将根据优先级进行修正。规则确保项目一致性、可维护性和协作效率。
</compliance>
</user_rules>

## 开发注意事项

### 技术实现特点
- **浏览器自动化**：基于Playwright，支持无头和可见模式
- **DOM处理**：增强缓存和智能过滤，避免LLM幻觉
- **统一架构**：WebAgent集成浏览器和DOM操作，消除通信开销
- **工作流编排**：LangGraph实现的智能状态管理和条件路由
- **类型安全**：完整的TypeScript风格类型提示
- **错误恢复**：基于LangGraph的自动重试和错误处理
- **代码生成**：支持操作录制和自动Python代码生成
- **性能优化**：全局浏览器实例复用，智能工具选择

### 架构设计原则
- **统一架构**：WebAgent集成浏览器操作、DOM分析和搜索功能，消除Agent间通信开销
- **LangGraph编排**：基于状态机的智能工作流管理，支持复杂任务分解和人机交互
- **工具分类**：浏览器工具（14个）+ DOM工具（12个）+ LLM驱动工具（12个）
- **全局搜索**：主要Agent都集成Google搜索，提供智能信息检索能力
- **智能路由**：基于LangGraph的条件路由和状态管理

## 架构演进和未来规划

### 已实现的v2.0核心功能
项目已经成功完成了从传统多Agent系统向LangGraph智能工作流的迁移：

#### 已完成的核心变革
- **统一WebAgent**：集成浏览器操作、DOM分析和搜索功能，消除Agent间通信开销
- **LangGraph编排**：实现了基于状态机的智能工作流管理和人机交互
- **PlannerAgent**：实现了自然语言任务理解和分解，集成搜索最佳实践能力
- **ValidatorAgent**：支持结果验证和质量检查
- **PromptOptimizationAgent**：智能提示词优化和模板生成
- **全局搜索能力**：所有主要Agent都集成Google搜索，提供智能信息检索

#### 当前实现的端到端流程
```
自然语言描述 → 任务规划 → Web自动化执行 → 结果验证 → 循环/终止
```

### 未来增强方向（v1.2+）

#### 高优先级改进
- **实现缺失组件**：添加CrawlerAgent（主协调）和RecordAgent（代码生成）
- **完善测试体系**：建立tests/unit、tests/integration、tests/e2e目录
- **建立文档体系**：创建docs/目录和相关文档
- **代码生成模块**：实现codegen/目录和代码生成功能

#### 技术增强
- **更高级的日期选择器处理**：专门的日期组件自动化工具
- **更智能的错误恢复**：基于AI的错误诊断和自动修复
- **多模态理解**：集成视觉理解和截图分析能力
- **性能优化**：更快的任务执行和资源管理

## 📚 文档体系

### 当前文档
- **项目说明**：`README.md` - 项目概述和快速开始
- **Claude Code指导**：`CLAUDE.md` - Claude Code工作指导和架构说明

> ⚠️ **待完善**：`docs/`目录和完整文档体系尚未建立

## 架构合规性和质量评估

### ✅ 符合的架构原则
- **单一职责原则**：每个Agent和工具有明确职责
- **依赖倒置**：通过接口和抽象进行依赖管理  
- **模块化设计**：清晰的模块边界和分层结构
- **状态管理统一**：LangGraph提供统一状态管理

### ⚠️ 待改进项
1. **搜索依赖问题**：serpapi包的Client导入需要修复（应使用SerpApiClient）
2. **测试覆盖不足**：缺少完整的单元、集成和E2E测试体系
3. **文档体系缺失**：需要建立完整的技术文档和API文档

### 🎯 架构优势
- **LangGraph架构**：提供良好的可扩展性和状态管理
- **统一WebAgent**：简化系统复杂度，提高执行效率
- **全局搜索能力**：38个专业工具，主要Agent都集成Google搜索功能
- **智能工作流**：支持人机交互和条件路由的复杂任务执行
- **MCP集成**：支持外部系统无缝接入