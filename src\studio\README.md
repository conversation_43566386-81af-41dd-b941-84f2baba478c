# LangGraph Studio Integration

This directory contains the LangGraph Studio integration layer for iICrawlerMCP.

## Overview

LangGraph Studio provides a visual interface for debugging and monitoring the iICrawlerMCP workflow. This integration uses an adapter pattern to wrap the existing graph without modifying any source code.

## Prerequisites

- Python 3.9 or higher
- Docker Desktop 4.24+ (required for Studio)
- LangSmith account (free tier available)

## Quick Start

### 1. Install Dependencies

```bash
# Install LangGraph CLI
pip install langgraph-cli

# Ensure project dependencies are installed
pip install -e .
```

### 2. Launch Studio

**Option A: Using the launcher script (recommended)**
```bash
python src/studio/studio_launcher.py
```

**Option B: Direct command**
```bash
langgraph dev
```

### 3. Access Studio

Open your browser and navigate to:
```
https://smith.langchain.com/studio/?baseUrl=http://127.0.0.1:2024
```

## File Structure

```
src/studio/
├── graph_adapter.py      # Adapter that imports existing graph
├── studio_launcher.py    # Convenience launcher script
├── .env.studio          # Studio-specific environment overrides (optional)
└── README.md            # This file
```

## Architecture

The Studio integration follows these principles:
- **Zero modification**: No changes to existing source code
- **Adapter pattern**: Wraps existing graph for Studio compatibility
- **Environment isolation**: Separate configuration for Studio mode
- **Backward compatibility**: Existing Streamlit UI and scripts work unchanged

## Configuration

The integration uses the following configuration files:

### langgraph.json (Project Root)
Defines the graph location and dependencies for Studio.

### .env (Project Root)
Contains all environment variables including API keys.

## Workflow Visualization

In Studio, you'll see the following workflow nodes:
1. **prompt** - Optimizes user input
2. **planner** - Creates task execution plan
3. **web** - Executes web automation
4. **validator** - Validates results

## Debugging Features

Studio provides:
- Real-time execution tracking
- State inspection at each node
- Ability to pause and modify execution
- LangSmith tracing integration

## Browser Compatibility

- **Chrome/Chromium**: Recommended
- **Safari**: May have HTTP restrictions on localhost
- **Brave**: Disable Shields for localhost
- **Firefox**: Supported

## Troubleshooting

### "Failed to load assistants" error
1. Use Chrome instead of Safari
2. Ensure Docker is running
3. Check port 2024 is not in use

### Docker not found
1. Install Docker Desktop from https://www.docker.com/products/docker-desktop
2. Start Docker Desktop
3. Try launching Studio again

### Port already in use
```bash
# Find process using port 2024
netstat -ano | findstr :2024

# Kill the process
taskkill /PID <process_id> /F
```

### Environment variables not loading
Ensure your `.env` file is in the project root and contains required keys:
- OPENAI_API_KEY
- LANGCHAIN_API_KEY (for tracing)

## Advanced Usage

### Custom Port
```bash
langgraph dev --port 3000
```
Then access at: `https://smith.langchain.com/studio/?baseUrl=http://127.0.0.1:3000`

### Production Mode
```bash
langgraph up
```

### Docker Network
When accessing local services from within Studio, use `host.docker.internal` instead of `localhost`.

## Support

For issues specific to:
- **Studio integration**: Check this README and studio_launcher.py
- **iICrawlerMCP**: See main project documentation
- **LangGraph Studio**: Visit https://docs.langchain.com/langgraph-platform/langgraph-studio