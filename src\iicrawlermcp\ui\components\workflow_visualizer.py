"""
LangGraph工作流可视化组件

提供清晰的工作流执行进度可视化，展示Planner -> WebAgent -> Validator的执行流程。
"""

import streamlit as st
from typing import Dict, Any, Optional
from ..state_manager import WorkflowState


def render_workflow_visualizer(workflow_state: WorkflowState):
    """
    渲染工作流可视化组件（包含执行统计）
    
    Args:
        workflow_state: 当前工作流状态
    """
    # 标题和统计信息在同一行
    col1, col2, col3 = st.columns([2, 1, 1])
    
    with col1:
        st.markdown("### 🎯 LangGraph 工作流")
    
    with col2:
        if workflow_state.current_status == "running":
            st.metric("⏱️ 执行时间", f"{workflow_state.execution_time:.1f}秒")
    
    with col3:
        if workflow_state.current_status == "running":
            st.metric("📊 步骤", workflow_state.step_count)
    
    # 定义工作流节点
    nodes = [
        {
            "key": "planner",
            "name": "任务规划",
            "icon": "🤔",
            "description": "分析任务，制定执行计划"
        },
        {
            "key": "web", 
            "name": "Web执行",
            "icon": "🕷️",
            "description": "执行Web自动化操作"
        },
        {
            "key": "validator",
            "name": "结果验证", 
            "icon": "✅",
            "description": "验证执行结果"
        }
    ]
    
    # 创建列布局
    cols = st.columns([1, 0.3, 1, 0.3, 1])
    
    # 渲染节点和箭头
    for i, node in enumerate(nodes):
        with cols[i * 2]:
            render_workflow_node(node, workflow_state)
        
        # 渲染箭头（最后一个节点后不需要）
        if i < len(nodes) - 1:
            with cols[i * 2 + 1]:
                render_workflow_arrow(
                    from_status=workflow_state.get_node_status(node["key"]),
                    to_status=workflow_state.get_node_status(nodes[i + 1]["key"])
                )


def render_workflow_node(node: Dict[str, str], workflow_state: WorkflowState):
    """
    渲染单个工作流节点
    
    Args:
        node: 节点信息
        workflow_state: 工作流状态
    """
    status = workflow_state.get_node_status(node["key"])
    
    # 根据状态选择样式
    if status == "running":
        container_class = "workflow-node-running"
        status_emoji = "🔄"
    elif status == "completed":
        container_class = "workflow-node-completed"
        status_emoji = "✅"
    elif status == "error":
        container_class = "workflow-node-error"
        status_emoji = "❌"
    else:
        container_class = "workflow-node-pending"
        status_emoji = "⏸️"
    
    # 使用HTML渲染节点
    st.markdown(f"""
    <div class="{container_class}">
        <div style="font-size: 2em; margin-bottom: 5px;">
            {node['icon']}
        </div>
        <div style="font-weight: bold; margin-bottom: 3px;">
            {node['name']}
        </div>
        <div style="font-size: 0.8em; color: #666;">
            {node['description']}
        </div>
        <div style="margin-top: 5px;">
            {status_emoji} {status.upper()}
        </div>
    </div>
    """, unsafe_allow_html=True)


def render_workflow_arrow(from_status: str, to_status: str):
    """
    渲染工作流箭头
    
    Args:
        from_status: 起始节点状态
        to_status: 目标节点状态
    """
    # 根据状态决定箭头样式
    if from_status == "completed":
        if to_status == "running":
            arrow_class = "workflow-arrow-active"
            arrow = "➜"
        elif to_status == "completed":
            arrow_class = "workflow-arrow-completed"
            arrow = "→"
        else:
            arrow_class = "workflow-arrow-pending"
            arrow = "→"
    else:
        arrow_class = "workflow-arrow-pending"
        arrow = "→"
    
    st.markdown(f"""
    <div class="{arrow_class}" style="text-align: center; padding-top: 30px; font-size: 1.5em;">
        {arrow}
    </div>
    """, unsafe_allow_html=True)


def render_mini_workflow(workflow_state: WorkflowState) -> str:
    """
    渲染迷你工作流指示器（用于紧凑显示）
    
    Args:
        workflow_state: 工作流状态
        
    Returns:
        HTML字符串
    """
    nodes = ["planner", "web", "validator"]
    icons = {"planner": "🤔", "web": "🕷️", "validator": "✅"}
    
    html_parts = []
    for node in nodes:
        status = workflow_state.get_node_status(node)
        if status == "completed":
            color = "#28a745"
        elif status == "running":
            color = "#007bff"
        elif status == "error":
            color = "#dc3545"
        else:
            color = "#6c757d"
        
        html_parts.append(
            f'<span style="color: {color}; margin: 0 5px;">{icons[node]}</span>'
        )
    
    return " → ".join(html_parts)