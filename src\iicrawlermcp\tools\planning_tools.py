"""
Planning tools for task decomposition and execution planning.

This module provides tools that enable intelligent task planning without
complex rules - letting LLM make decisions about how to approach tasks.
"""

import json
import logging
from langchain_core.tools import tool

logger = logging.getLogger(__name__)


@tool
def generate_task_plan(task_description: str) -> str:
    """
    生成任务执行计划 - 让LLM分析任务并创建结构化执行计划。

    不使用复杂规则，而是让LLM理解任务需求并决定：
    - 需要哪些步骤
    - 步骤的执行顺序
    - 每个步骤需要哪个Agent
    - 步骤之间的依赖关系

    Args:
        task_description: 用户的任务描述

    Returns:
        包含执行计划的JSON字符串，格式：
        {
            "task": "任务描述",
            "complexity": "simple/moderate/complex",
            "steps": [
                {
                    "step_id": 1,
                    "description": "步骤描述",
                    "agent": "browser/element/prompt",
                    "tool": "具体的委托工具名",
                    "dependencies": [],
                    "expected_output": "预期结果"
                }
            ],
            "total_steps": 3,
            "requires_validation": true
        }

    Example:
        generate_task_plan("导航到google.com并搜索'人工智能'")
    """
    try:
        from langchain_openai import ChatOpenAI
        from ..core.config import config

        llm = ChatOpenAI(**config.get_llm_config())

        prompt = f"""
你是一个任务规划专家。请分析以下任务并生成执行计划。

任务：{task_description}

重要规则：
1. 你必须且只能输出JSON格式，不要包含任何其他文本、解释或markdown标记
2. 不要使用```json```代码块，直接输出JSON
3. 确保JSON格式完整且有效

步骤分配原则：
- browser agent用于：导航到URL、点击按钮、填写表单、输入文本、截图等浏览器操作
- element agent用于：查找元素、分析页面结构、提取数据
- 重要：如果任务包含"打开网页"或"导航"，第一步必须使用browser agent

JSON输出模板：
{{
  "task_complexity": "simple",
  "steps": [
    {{
      "step_id": 1,
      "description": "步骤描述",
      "agent": "browser",
      "tool": "smart_browser_action_finder",
      "dependencies": [],
      "expected_output": "预期结果"
    }}
  ],
  "total_steps": 1,
  "requires_validation": true
}}

现在输出JSON计划：
{{"""

        # 添加预填充的开始大括号来强制JSON输出
        response = llm.invoke(prompt)
        plan_json = response.content.strip()

        # 如果响应不是以{开头，尝试添加缺失的部分
        if not plan_json.startswith("{"):
            plan_json = "{" + plan_json

        # 尝试解析JSON以验证格式
        try:
            plan = json.loads(plan_json)
            logger.info(f"Generated plan with {plan.get('total_steps', 0)} steps")
            return plan_json
        except json.JSONDecodeError as e:
            logger.warning(f"JSON decode error: {e}")
            # 尝试多种提取策略
            import re

            # 策略1：提取markdown代码块中的JSON
            code_block_match = re.search(r"```(?:json)?\s*([\s\S]*?)```", plan_json)
            if code_block_match:
                try:
                    extracted = code_block_match.group(1).strip()
                    plan = json.loads(extracted)
                    logger.info("Successfully extracted JSON from code block")
                    return json.dumps(plan)
                except:
                    pass

            # 策略2：提取最外层的大括号内容（处理嵌套）
            brace_count = 0
            start_idx = -1
            end_idx = -1

            for i, char in enumerate(plan_json):
                if char == "{":
                    if start_idx == -1:
                        start_idx = i
                    brace_count += 1
                elif char == "}":
                    brace_count -= 1
                    if brace_count == 0 and start_idx != -1:
                        end_idx = i + 1
                        break

            if start_idx != -1 and end_idx != -1:
                try:
                    extracted = plan_json[start_idx:end_idx]
                    plan = json.loads(extracted)
                    logger.info("Successfully extracted JSON using brace matching")
                    return json.dumps(plan)
                except:
                    pass

            # 策略3：简单的正则提取
            json_match = re.search(r"\{[\s\S]*\}", plan_json)
            if json_match:
                try:
                    plan = json.loads(json_match.group())
                    return json.dumps(plan)
                except:
                    pass

            # 返回默认计划
            default_plan = {
                "task": task_description,
                "complexity": "simple",
                "steps": [
                    {
                        "step_id": 1,
                        "description": task_description,
                        "agent": "browser",
                        "tool": "smart_browser_action_finder",
                        "dependencies": [],
                        "expected_output": "任务完成",
                    }
                ],
                "total_steps": 1,
                "requires_validation": True,
            }
            return json.dumps(default_plan, ensure_ascii=False)

    except Exception as e:
        logger.error(f"Failed to generate task plan: {e}")
        return json.dumps(
            {
                "error": str(e),
                "task": task_description,
                "complexity": "unknown",
                "steps": [],
                "total_steps": 0,
            },
            ensure_ascii=False,
        )


@tool
def identify_required_agents(task_description: str) -> str:
    """
    识别任务需要哪些Agent参与 - 让LLM分析任务并确定需要的Agent组合。

    Args:
        task_description: 任务描述

    Returns:
        需要的Agent列表和原因说明

    Example:
        identify_required_agents("在网页上查找所有按钮并点击第一个")
        返回: "需要ElementAgent查找按钮，BrowserAgent执行点击"
    """
    try:
        from langchain_openai import ChatOpenAI
        from ..core.config import config

        llm = ChatOpenAI(**config.get_llm_config())

        prompt = f"""
分析以下任务，确定需要哪些专业Agent参与：

任务：{task_description}

可用的Agent：
1. BrowserAgent - 浏览器操作（导航、点击、输入、截图）
2. ElementAgent - DOM分析（查找元素、提取数据、分析结构）
3. PromptOptimizationAgent - 提示词优化（改进不清晰的指令）

请分析任务并说明：
1. 需要哪些Agent
2. 每个Agent负责什么
3. 执行顺序建议

简洁回答。
"""

        response = llm.invoke(prompt)
        result = response.content.strip()

        logger.info(f"Identified required agents for task")
        return result

    except Exception as e:
        logger.error(f"Failed to identify required agents: {e}")
        return f"错误：{str(e)}"


@tool
def optimize_execution_order(plan_json: str) -> str:
    """
    优化执行计划中的步骤顺序 - 让LLM分析哪些步骤可以并行执行。

    Args:
        plan_json: 执行计划的JSON字符串

    Returns:
        优化后的执行计划，标注了可并行执行的步骤
    """
    try:
        plan = json.loads(plan_json)

        from langchain_openai import ChatOpenAI
        from ..core.config import config

        llm = ChatOpenAI(**config.get_llm_config())

        prompt = f"""
分析以下执行计划，优化执行顺序：

{json.dumps(plan, ensure_ascii=False, indent=2)}

请识别：
1. 哪些步骤可以并行执行（没有数据依赖）
2. 哪些步骤必须顺序执行
3. 优化建议

返回优化后的JSON计划，为可并行的步骤添加 "can_parallel": true 标记。
"""

        response = llm.invoke(prompt)
        optimized_json = response.content.strip()

        # 提取JSON
        import re

        json_match = re.search(r"\{[\s\S]*\}", optimized_json)
        if json_match:
            return json_match.group()

        return plan_json  # 如果优化失败，返回原计划

    except Exception as e:
        logger.error(f"Failed to optimize execution order: {e}")
        return plan_json


# 工具列表
PLANNING_TOOLS = [
    generate_task_plan,
    identify_required_agents,
    optimize_execution_order,
]


def get_planning_tools():
    """
    Get planning tools for PlannerAgent.

    Returns:
        List of planning tools
    """
    return PLANNING_TOOLS.copy()
