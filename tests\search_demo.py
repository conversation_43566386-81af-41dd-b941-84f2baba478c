import os
import json
import logging
from curl_cffi import requests
from typing import Optional, Dict, Any, List
from dotenv import load_dotenv
from datetime import datetime
import time

from serpapi import Client


load_dotenv()

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class SmartSearchService:
    """智能搜索服务：集成SERP搜索和Jina内容提取"""
    
    def __init__(self):
        self.serpapi_key = os.getenv('SERPAPI_API_KEY')
        self.jina_key = os.getenv('JINA_API_KEY', 'jina_ab740982821f4e2b9917abf420d43771O30JtSBV34nXCJXo-N0yMK_tIwHl')
        self.cache = {}
        self.cache_ttl = 3600  # 1小时缓存
        
        if not self.serpapi_key:
            logging.warning("SERPAPI_API_KEY not found in .env file")
    
    def search_links(
        self,
        query: str,
        num_results: int = 10,
        search_type: Optional[str] = None,
        location: Optional[str] = None
    ) -> List[Dict[str, str]]:
        """
        步骤1: 使用SERP API搜索获取链接列表
        
        :param query: 搜索查询
        :param num_results: 返回结果数量
        :param search_type: 搜索类型 (None=网页, "nws"=新闻, "isch"=图片)
        :param location: 搜索位置
        :return: 搜索结果列表
        """
        if not self.serpapi_key:
            logging.error("SERPAPI_API_KEY is required")
            return []
        
        cache_key = f"serp_{query}_{num_results}_{search_type}_{location}"
        if cache_key in self.cache:
            cached_time, cached_data = self.cache[cache_key]
            if time.time() - cached_time < self.cache_ttl:
                logging.info(f"Using cached SERP results for: {query}")
                return cached_data
        
        try:
            logging.info(f"Searching for: {query}")
            # 使用新的 Client API
            client = Client(api_key=self.serpapi_key)
            
            # 构建完整的搜索参数
            search_params = {
                'engine': 'google',
                'q': query,
                'num': num_results,
                'hl': 'zh-cn',  # 界面语言
                'gl': 'cn',     # 国家/地区
                'safe': 'off',  # 安全搜索
                'start': 0,     # 起始位置
                'filter': 1,    # 过滤相似结果
            }
            
            # 可选参数
            if search_type:
                search_params["tbm"] = search_type  # nws=新闻, isch=图片, vid=视频
            if location:
                search_params["location"] = location
            
            # 添加设备类型（可选）
            # search_params["device"] = "desktop"  # 或 "mobile", "tablet"
            
            results = client.search(search_params)
            
            links = []
            if "organic_results" in results:
                for result in results["organic_results"]:
                    links.append({
                        "title": result.get("title", ""),
                        "link": result.get("link", ""),
                        "snippet": result.get("snippet", ""),
                        "position": result.get("position", 0)
                    })
            
            self.cache[cache_key] = (time.time(), links)
            logging.info(f"Found {len(links)} search results")
            return links
            
        except Exception as e:
            logging.error(f"SERP search failed: {str(e)}")
            return []
    
    def extract_content(
        self,
        url: str,
        return_format: str = "markdown",
        with_images: bool = False,
        timeout: int = 30,
        css_selector: Optional[str] = None
    ) -> Optional[str]:
        """
        步骤2: 使用Jina Reader提取网页内容
        
        :param url: 要提取内容的URL
        :param return_format: 返回格式 (markdown, html, text, screenshot)
        :param with_images: 是否包含图片描述
        :param timeout: 超时时间(秒)
        :param css_selector: CSS选择器，只提取特定部分
        :return: 提取的内容
        """
        cache_key = f"jina_{url}_{return_format}"
        if cache_key in self.cache:
            cached_time, cached_data = self.cache[cache_key]
            if time.time() - cached_time < self.cache_ttl:
                logging.info(f"Using cached content for: {url}")
                return cached_data
        
        try:
            headers = {
                "Authorization": f"Bearer {self.jina_key}",
                "Accept": "application/json",
                "X-Return-Format": return_format,
                "X-Timeout": str(timeout)
            }
            
            if with_images:
                headers["X-With-Generated-Alt"] = "true"
                headers["X-With-Images-Summary"] = "true"
            
            if css_selector:
                headers["X-Target-Selector"] = css_selector
            
            jina_url = f"https://r.jina.ai/{url}"
            logging.info(f"Extracting content from: {url}")
            
            response = requests.get(jina_url, headers=headers, timeout=timeout+5)
            
            if response.status_code == 200:
                data = response.json()
                content = data.get("data", {}).get("content", "")
                self.cache[cache_key] = (time.time(), content)
                logging.info(f"Successfully extracted {len(content)} characters")
                return content
            else:
                logging.error(f"Jina API error: {response.status_code}")
                return None
                
        except Exception as e:
            logging.error(f"Content extraction failed: {str(e)}")
            return None
    
    def search_and_extract(
        self,
        query: str,
        num_links: int = 3,
        extract_all: bool = False
    ) -> Dict[str, Any]:
        """
        完整流程：搜索并提取内容
        
        :param query: 搜索查询
        :param num_links: 提取前N个链接的内容
        :param extract_all: 是否提取所有搜索结果
        :return: 搜索结果和提取的内容
        """
        results = {
            "query": query,
            "timestamp": datetime.now().isoformat(),
            "search_results": [],
            "extracted_contents": []
        }
        
        # 步骤1: 搜索
        links = self.search_links(query, num_results=10)
        results["search_results"] = links
        
        if not links:
            logging.warning("No search results found")
            return results
        
        # 步骤2: 提取内容
        links_to_extract = links if extract_all else links[:num_links]
        
        for idx, link_info in enumerate(links_to_extract, 1):
            url = link_info["link"]
            logging.info(f"Extracting content {idx}/{len(links_to_extract)}: {link_info['title']}")
            
            content = self.extract_content(url)
            if content:
                results["extracted_contents"].append({
                    "title": link_info["title"],
                    "url": url,
                    "snippet": link_info["snippet"],
                    "content": content[:5000],  # 限制长度
                    "content_length": len(content)
                })
            
            # 避免请求过快
            if idx < len(links_to_extract):
                time.sleep(0.5)
        
        return results
    
    def search_with_jina(self, query: str) -> Optional[str]:
        """
        使用Jina的搜索端点（包含搜索和内容提取）
        
        :param query: 搜索查询
        :return: 搜索并提取的内容
        """
        try:
            headers = {
                "Authorization": f"Bearer {self.jina_key}",
                "Accept": "application/json"
            }
            
            jina_search_url = f"https://s.jina.ai/?q={requests.utils.quote(query)}"
            logging.info(f"Jina search for: {query}")
            
            response = requests.get(jina_search_url, headers=headers, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                content = data.get("data", {}).get("content", "")
                logging.info(f"Jina search returned {len(content)} characters")
                return content
            else:
                logging.error(f"Jina search error: {response.status_code}")
                return None
                
        except Exception as e:
            logging.error(f"Jina search failed: {str(e)}")
            return None
    
    def smart_query_analysis(self, query: str) -> Dict[str, Any]:
        """
        智能分析查询意图，决定搜索策略
        
        :param query: 用户查询
        :return: 分析结果和建议的搜索策略
        """
        analysis = {
            "query": query,
            "search_type": "general",
            "needs_recent": False,
            "is_technical": False,
            "is_definition": False,
            "suggested_actions": []
        }
        
        # 时效性检测
        temporal_keywords = ["最新", "2024", "2025", "今年", "本月", "recent", "latest", "current"]
        if any(keyword in query.lower() for keyword in temporal_keywords):
            analysis["needs_recent"] = True
            analysis["suggested_actions"].append("search_news")
        
        # 技术术语检测
        tech_keywords = ["API", "框架", "算法", "代码", "函数", "错误", "error", "framework", "library"]
        if any(keyword in query.lower() for keyword in tech_keywords):
            analysis["is_technical"] = True
            analysis["suggested_actions"].append("extract_documentation")
        
        # 定义性查询检测
        definition_keywords = ["什么是", "如何", "为什么", "what is", "how to", "why"]
        if any(keyword in query.lower() for keyword in definition_keywords):
            analysis["is_definition"] = True
            analysis["suggested_actions"].append("extract_full_content")
        
        # 根据分析结果推荐搜索策略
        if analysis["needs_recent"]:
            analysis["search_type"] = "news"
        elif analysis["is_technical"]:
            analysis["search_type"] = "technical"
        
        return analysis


def demo_basic_usage():
    """基础使用示例"""
    print("\n" + "="*60)
    print("智能搜索服务 - 基础使用示例")
    print("="*60)
    
    service = SmartSearchService()
    
    # 示例1: 搜索链接
    print("\n1. 搜索链接示例:")
    links = service.search_links("Python异步编程最佳实践", num_results=5)
    for idx, link in enumerate(links[:3], 1):
        print(f"   {idx}. {link['title']}")
        print(f"      {link['link']}")
    
    # 示例2: 提取单个网页内容
    if links:
        print("\n2. 提取网页内容示例:")
        content = service.extract_content(links[0]['link'])
        if content:
            print(f"   提取成功，内容长度: {len(content)} 字符")
            print(f"   内容预览: {content[:200]}...")
    
    # 示例3: 使用Jina搜索（搜索+提取一体）
    print("\n3. Jina一体化搜索示例:")
    jina_content = service.search_with_jina("machine learning trends 2024")
    if jina_content:
        print(f"   Jina搜索成功，内容长度: {len(jina_content)} 字符")
        print(f"   内容预览: {jina_content[:200]}...")


def demo_advanced_usage():
    """高级使用示例"""
    print("\n" + "="*60)
    print("智能搜索服务 - 高级使用示例")
    print("="*60)
    
    service = SmartSearchService()
    
    # 示例1: 完整流程（搜索+批量提取）
    print("\n1. 完整搜索和提取流程:")
    results = service.search_and_extract(
        query="如何优化大语言模型推理速度",
        num_links=2
    )
    
    print(f"   找到 {len(results['search_results'])} 个搜索结果")
    print(f"   成功提取 {len(results['extracted_contents'])} 个网页内容")
    
    for content in results['extracted_contents']:
        print(f"\n   标题: {content['title']}")
        print(f"   内容长度: {content['content_length']} 字符")
        print(f"   摘要: {content['snippet']}")
    
    # 示例2: 智能查询分析
    print("\n2. 智能查询分析示例:")
    queries = [
        "最新的AI发展趋势",
        "什么是RAG技术",
        "Python asyncio错误处理"
    ]
    
    for query in queries:
        analysis = service.smart_query_analysis(query)
        print(f"\n   查询: {query}")
        print(f"   类型: {analysis['search_type']}")
        print(f"   需要最新信息: {analysis['needs_recent']}")
        print(f"   技术相关: {analysis['is_technical']}")
        print(f"   建议操作: {', '.join(analysis['suggested_actions'])}")


def demo_agent_integration():
    """Agent集成示例"""
    print("\n" + "="*60)
    print("智能搜索服务 - Agent集成示例")
    print("="*60)
    
    service = SmartSearchService()
    
    class SimpleAgent:
        def __init__(self, search_service):
            self.search = search_service
        
        def answer_question(self, question: str) -> str:
            """模拟Agent回答问题的流程"""
            print(f"\nAgent处理问题: {question}")
            
            # 1. 分析查询
            analysis = self.search.smart_query_analysis(question)
            print(f"  查询类型: {analysis['search_type']}")
            
            # 2. 根据分析结果选择搜索策略
            if analysis['needs_recent']:
                print("  策略: 搜索最新信息")
                results = self.search.search_and_extract(question, num_links=3)
            elif analysis['is_technical']:
                print("  策略: 深度技术内容提取")
                results = self.search.search_and_extract(question, num_links=2)
            else:
                print("  策略: 快速Jina搜索")
                content = self.search.search_with_jina(question)
                return f"基于搜索结果的回答: {content[:500]}..." if content else "未找到相关信息"
            
            # 3. 整合结果
            if results and results['extracted_contents']:
                combined = "\n".join([c['content'][:500] for c in results['extracted_contents']])
                return f"综合{len(results['extracted_contents'])}个来源的信息:\n{combined[:1000]}..."
            
            return "未找到相关信息"
    
    # 创建Agent并测试
    agent = SimpleAgent(service)
    
    test_questions = [
        "React 19有哪些新特性",
        "什么是向量数据库",
        "如何处理Python内存泄漏"
    ]
    
    for question in test_questions[:1]:  # 只测试第一个问题
        answer = agent.answer_question(question)
        print(f"\nAgent回答预览:")
        print(f"  {answer[:300]}...")


if __name__ == "__main__":
    print("""
╔══════════════════════════════════════════════════════════╗
║           智能搜索服务演示 (SERP + Jina Reader)          ║
╚══════════════════════════════════════════════════════════╝

配置要求:
1. 在.env文件中设置 SERPAPI_API_KEY
2. Jina API Key已内置（可选配置）

功能特性:
✓ SERP API搜索获取链接
✓ Jina Reader提取网页内容
✓ 智能查询分析
✓ 结果缓存
✓ Agent集成示例
    """)
    
    # 运行演示
    try:
        demo_basic_usage()
        print("\n" + "-"*60)
        demo_advanced_usage()
        print("\n" + "-"*60)
        demo_agent_integration()
        
        print("\n" + "="*60)
        print("演示完成！你可以根据需要修改和扩展这些功能。")
        print("="*60)
        
    except KeyboardInterrupt:
        print("\n\n演示被用户中断")
    except Exception as e:
        print(f"\n演示过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()