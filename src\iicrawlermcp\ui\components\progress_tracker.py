"""
实时进度跟踪组件

提供执行步骤的时间轴视图，实时显示Agent的思考和执行过程。
"""

import streamlit as st
from typing import List, Optional
from datetime import datetime
from ..state_manager import ExecutionStep, WorkflowState


def render_progress_tracker(workflow_state: WorkflowState, max_steps: int = 10):
    """
    渲染进度跟踪组件
    
    Args:
        workflow_state: 工作流状态
        max_steps: 最多显示的步骤数
    """
    st.markdown("### 📊 实时执行进度")
    
    # 创建容器
    progress_container = st.container()
    
    with progress_container:
        # 显示执行统计
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("执行时间", f"{workflow_state.execution_time:.1f}秒")
        
        with col2:
            st.metric("执行步骤", workflow_state.step_count)
        
        with col3:
            status_emoji = {
                "idle": "⏸️",
                "running": "🔄",
                "completed": "✅",
                "error": "❌"
            }.get(workflow_state.current_status, "❓")
            st.metric("当前状态", f"{status_emoji} {workflow_state.current_status.upper()}")
        
        with col4:
            if workflow_state.current_node:
                st.metric("当前节点", workflow_state.current_node.upper())
            else:
                st.metric("当前节点", "N/A")
        
        # 只显示当前执行步骤
        if workflow_state.execution_steps and workflow_state.current_status == "running":
            st.markdown("#### 📋 当前步骤")
            # 只显示最后一个步骤（当前正在执行的）
            current_step = workflow_state.execution_steps[-1]
            render_current_step(current_step)
        elif workflow_state.current_status == "completed":
            st.markdown("#### ✅ 执行完成")
            st.success(f"共执行 {workflow_state.step_count} 个步骤")
        elif workflow_state.current_status == "error":
            st.markdown("#### ❌ 执行失败")
            if workflow_state.execution_steps:
                last_step = workflow_state.execution_steps[-1]
                st.error(f"失败步骤: {last_step.node} - {last_step.content[:100]}...")


def render_current_step(step: ExecutionStep):
    """
    渲染当前执行步骤
    
    Args:
        step: 当前执行步骤
    """
    # 选择图标
    node_icons = {
        "planner": "🤔",
        "web": "🕷️",
        "validator": "✅",
        "system": "⚙️"
    }
    icon = node_icons.get(step.node, "📝")
    
    # 选择状态颜色
    status_colors = {
        "completed": "#28a745",
        "running": "#007bff",
        "error": "#dc3545",
        "pending": "#6c757d"
    }
    color = status_colors.get(step.status, "#000000")
    
    # 格式化时间
    time_str = step.timestamp.strftime("%H:%M:%S")
    
    # 渲染当前步骤卡片
    st.markdown(f"""
    <div style="
        background: linear-gradient(135deg, {color}22 0%, {color}11 100%);
        border-left: 4px solid {color};
        border-radius: 8px;
        padding: 15px;
        margin: 10px 0;
    ">
        <div style="display: flex; align-items: center; margin-bottom: 10px;">
            <span style="font-size: 1.5em; margin-right: 10px;">{icon}</span>
            <strong style="color: {color}; font-size: 1.1em;">
                {step.node.upper()} - {step.action}
            </strong>
            <span style="margin-left: auto; color: #666; font-size: 0.9em;">
                {time_str}
            </span>
        </div>
        <div style="color: #333; font-size: 0.95em;">
            {step.content[:200]}{"..." if len(step.content) > 200 else ""}
        </div>
    </div>
    """, unsafe_allow_html=True)


def render_execution_timeline(steps: List[ExecutionStep], show_full: bool = False):
    """
    渲染执行时间轴
    
    Args:
        steps: 执行步骤列表
        show_full: 是否显示完整内容
    """
    # 使用expander进行折叠显示
    for i, step in enumerate(reversed(steps)):
        # 选择图标
        node_icons = {
            "planner": "🤔",
            "web": "🕷️",
            "validator": "✅",
            "system": "⚙️"
        }
        icon = node_icons.get(step.node, "📝")
        
        # 选择状态颜色
        status_colors = {
            "completed": "#28a745",
            "running": "#007bff",
            "error": "#dc3545",
            "pending": "#6c757d"
        }
        color = status_colors.get(step.status, "#000000")
        
        # 格式化时间
        time_str = step.timestamp.strftime("%H:%M:%S")
        
        # 渲染步骤
        if show_full:
            with st.expander(f"{icon} [{time_str}] {step.node.upper()} - {step.action}", expanded=(i == 0)):
                st.markdown(f"""
                <div style="color: {color};">
                    <strong>节点:</strong> {step.node}<br>
                    <strong>动作:</strong> {step.action}<br>
                    <strong>状态:</strong> {step.status}<br>
                    <strong>内容:</strong><br>
                    <pre style="white-space: pre-wrap; word-wrap: break-word;">
                    {step.content}
                    </pre>
                </div>
                """, unsafe_allow_html=True)
        else:
            # 简洁显示
            content_preview = step.content[:100] + "..." if len(step.content) > 100 else step.content
            st.markdown(f"""
            <div class="timeline-item" style="border-left: 3px solid {color}; padding-left: 15px; margin: 10px 0;">
                <div style="color: {color}; font-weight: bold;">
                    {icon} [{time_str}] {step.node.upper()}
                </div>
                <div style="color: #666; font-size: 0.9em; margin-top: 5px;">
                    {content_preview}
                </div>
            </div>
            """, unsafe_allow_html=True)


def render_progress_bar(workflow_state: WorkflowState):
    """
    渲染进度条
    
    Args:
        workflow_state: 工作流状态
    """
    # 计算进度
    total_nodes = 3  # planner, web, validator
    completed_nodes = sum(
        1 for node in ["planner", "web", "validator"]
        if workflow_state.get_node_status(node) == "completed"
    )
    
    progress = completed_nodes / total_nodes
    
    # 显示进度条
    st.progress(progress)
    st.caption(f"进度: {completed_nodes}/{total_nodes} 节点完成")


def render_realtime_monitor(workflow_state: WorkflowState):
    """
    渲染实时监控面板（紧凑版）
    
    Args:
        workflow_state: 工作流状态
    """
    # 创建一个紧凑的监控面板
    monitor_html = f"""
    <div style="
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 15px;
        border-radius: 10px;
        margin: 10px 0;
    ">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <div>
                <strong>🚀 任务执行中</strong>
            </div>
            <div style="display: flex; gap: 20px;">
                <span>⏱️ {workflow_state.execution_time:.1f}s</span>
                <span>📊 {workflow_state.step_count} steps</span>
                <span>📍 {workflow_state.current_node or 'N/A'}</span>
            </div>
        </div>
    </div>
    """
    
    st.markdown(monitor_html, unsafe_allow_html=True)