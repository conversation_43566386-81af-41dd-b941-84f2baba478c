# Studio-specific environment variables
# These override the defaults when running in Studio mode

# OpenAI Configuration
OPENAI_API_KEY=${OPENAI_API_KEY}
OPENAI_MODEL=gpt-4-turbo

# LangChain Tracing (for Studio debugging)
LANGCHAIN_TRACING_V2=true
LANGCHAIN_PROJECT=iICrawlerMCP-Studio
LANGCHAIN_API_KEY=${LANGCHAIN_API_KEY}

# Browser Configuration
HEADLESS=true
VERBOSE=false

# Screenshot Configuration
DEFAULT_SCREENSHOT_PATH=screenshots/

# Timeout Configuration (in seconds)
DEFAULT_TIMEOUT=30
MAX_ITERATIONS=50

# Debug Mode
DEBUG=false

# Docker Network Configuration
# Use host.docker.internal instead of localhost when accessing local services
LOCAL_HOST=host.docker.internal