#!/usr/bin/env python3
"""
iICrawlerMCP Streamlit UI 启动脚本

使用方法:
    python run_streamlit.py
    python run_streamlit.py --port 8501
    python run_streamlit.py --host 0.0.0.0 --port 8502
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path

def main():
    parser = argparse.ArgumentParser(description="启动 iICrawlerMCP Streamlit UI")
    parser.add_argument("--host", default="localhost", help="服务器地址 (默认: localhost)")
    parser.add_argument("--port", type=int, default=8501, help="服务器端口 (默认: 8501)")
    parser.add_argument("--headless", action="store_true", help="无头模式运行")
    parser.add_argument("--debug", action="store_true", help="调试模式")
    
    args = parser.parse_args()
    
    # 检查依赖
    try:
        import streamlit
        print(f"✅ Streamlit 版本: {streamlit.__version__}")
    except ImportError:
        print("❌ 未安装 Streamlit，正在安装...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "streamlit>=1.28.0"])
        print("✅ Streamlit 安装完成")
    
    try:
        import langchain_community
        print(f"✅ LangChain Community 已安装")
    except ImportError:
        print("❌ 未安装 langchain-community，正在安装...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "langchain-community>=0.0.20"])
        print("✅ LangChain Community 安装完成")
    
    # 检查配置文件
    env_file = Path(".env")
    if not env_file.exists():
        print("⚠️  未找到 .env 配置文件")
        print("请确保已创建 .env 文件并配置必要的参数")
        print("参考 .env.example 文件")
    else:
        print("✅ 找到配置文件 .env")
    
    # 构建启动命令
    app_path = "src/iicrawlermcp/ui/app.py"
    if not Path(app_path).exists():
        print(f"❌ 未找到应用文件: {app_path}")
        sys.exit(1)
    
    cmd = [
        "streamlit", "run", app_path,
        "--server.address", args.host,
        "--server.port", str(args.port)
    ]
    
    if args.headless:
        cmd.extend(["--server.headless", "true"])
    
    if args.debug:
        cmd.extend(["--logger.level", "debug"])
    
    # 设置环境变量
    env = os.environ.copy()
    if args.debug:
        env["STREAMLIT_LOGGER_LEVEL"] = "debug"
    
    print(f"🚀 启动 Streamlit UI...")
    print(f"📍 地址: http://{args.host}:{args.port}")
    print(f"🔧 命令: {' '.join(cmd)}")
    print("=" * 50)
    
    try:
        # 启动 Streamlit
        subprocess.run(cmd, env=env)
    except KeyboardInterrupt:
        print("\n👋 用户中断，正在关闭...")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
