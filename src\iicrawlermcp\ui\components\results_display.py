"""
智能结果展示组件

根据结果类型自动选择最佳展示方式，支持JSON、截图、文本等多种格式。
"""

import streamlit as st
import json
import os
from typing import Dict, Any, List, Optional
from datetime import datetime
from ..state_manager import WorkflowState


def render_results_display(workflow_state: WorkflowState):
    """
    渲染结果展示组件
    
    Args:
        workflow_state: 工作流状态
    """
    st.markdown("### ✅ 执行结果")
    
    if workflow_state.current_status == "idle":
        st.info("🔍 等待任务执行...")
        return
    
    if workflow_state.current_status == "running":
        st.info("⏳ 任务执行中，请稍候...")
        return
    
    if workflow_state.current_status == "error":
        render_error_result(workflow_state)
        return
    
    # 任务完成，展示结果
    if workflow_state.current_status == "completed":
        render_success_result(workflow_state)


def render_success_result(workflow_state: WorkflowState):
    """
    渲染成功结果
    
    Args:
        workflow_state: 工作流状态
    """
    # 显示执行统计
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("✅ 执行状态", "成功")
    
    with col2:
        st.metric("⏱️ 执行时间", f"{workflow_state.execution_time:.2f}秒")
    
    with col3:
        st.metric("📊 执行步骤", workflow_state.step_count)
    
    # 使用选项卡展示不同类型的结果
    tabs = st.tabs(["📊 结果数据", "📸 截图", "📝 执行日志", "🔍 原始输出"])
    
    with tabs[0]:
        render_result_data(workflow_state)
    
    with tabs[1]:
        render_screenshots()
    
    with tabs[2]:
        render_execution_log(workflow_state)
    
    with tabs[3]:
        render_raw_output(workflow_state)


def render_error_result(workflow_state: WorkflowState):
    """
    渲染错误结果
    
    Args:
        workflow_state: 工作流状态
    """
    st.error(f"❌ 任务执行失败")
    
    # 显示错误信息
    if workflow_state.error_message:
        st.markdown("#### 错误详情")
        st.code(workflow_state.error_message, language="text")
    
    # 显示失败前的执行步骤
    if workflow_state.execution_steps:
        with st.expander("🔍 查看执行日志"):
            for step in workflow_state.execution_steps[-10:]:
                if step.status == "error":
                    st.markdown(f"❌ **{step.node}**: {step.content}")
                else:
                    st.markdown(f"✅ **{step.node}**: {step.content[:100]}...")


def render_result_data(workflow_state: WorkflowState):
    """
    渲染结果数据
    
    Args:
        workflow_state: 工作流状态
    """
    if not workflow_state.final_result:
        st.info("暂无结构化结果数据")
        return
    
    result = workflow_state.final_result
    
    # 简单提取关键信息
    if isinstance(result, dict):
        # 查找validator节点的输出（通常包含最终结果）
        if "validator" in result and isinstance(result["validator"], dict):
            validator_data = result["validator"]
            
            # 查找execution_results
            if "execution_results" in validator_data:
                st.markdown("#### 📊 执行结果")
                for exec_result in validator_data["execution_results"][-1:]:  # 只显示最后一个
                    if "result" in exec_result and "output" in exec_result["result"]:
                        output = exec_result["result"]["output"]
                        display_formatted_content(output)
                        return
            
            # 查找messages
            if "messages" in validator_data and validator_data["messages"]:
                st.markdown("#### 📊 执行结果")
                last_message = validator_data["messages"][-1]
                if hasattr(last_message, "content"):
                    display_formatted_content(last_message.content)
                    return
        
        # 查找web节点的输出
        if "web" in result and isinstance(result["web"], dict):
            web_data = result["web"]
            
            if "execution_results" in web_data:
                st.markdown("#### 📊 执行结果")
                for exec_result in web_data["execution_results"][-1:]:  # 只显示最后一个
                    if "result" in exec_result and "output" in exec_result["result"]:
                        output = exec_result["result"]["output"]
                        display_formatted_content(output)
                        return
        
        # 如果找不到特定节点，显示所有execution_results
        st.markdown("#### 📊 执行结果")
        found_result = False
        for node_name, node_data in result.items():
            if isinstance(node_data, dict) and "execution_results" in node_data:
                for exec_result in node_data["execution_results"][-1:]:  # 每个节点只显示最后一个
                    if "result" in exec_result and "output" in exec_result["result"]:
                        output = exec_result["result"]["output"]
                        with st.expander(f"节点: {node_name}", expanded=True):
                            display_formatted_content(output)
                        found_result = True
                        break
        
        if not found_result:
            # 显示简化的原始数据
            filtered = {k: v for k, v in result.items() 
                       if not k.startswith("_") and k not in ["messages"] and v}
            if filtered:
                st.json(filtered)
            else:
                st.info("任务执行完成")
    else:
        display_formatted_content(str(result))


def render_screenshots():
    """渲染截图画廊"""
    screenshots_dir = "screenshots"
    
    if not os.path.exists(screenshots_dir):
        st.info("📷 暂无截图")
        return
    
    # 获取所有截图文件
    screenshot_files = [
        f for f in os.listdir(screenshots_dir)
        if f.endswith((".png", ".jpg", ".jpeg"))
    ]
    
    if not screenshot_files:
        st.info("📷 暂无截图")
        return
    
    # 按修改时间排序
    screenshot_files.sort(
        key=lambda x: os.path.getmtime(os.path.join(screenshots_dir, x)),
        reverse=True
    )
    
    st.markdown(f"#### 📸 生成的截图 ({len(screenshot_files)}张)")
    
    # 使用列布局展示截图
    cols = st.columns(min(3, len(screenshot_files)))
    
    for i, screenshot_file in enumerate(screenshot_files[:6]):  # 最多显示6张
        screenshot_path = os.path.join(screenshots_dir, screenshot_file)
        file_time = datetime.fromtimestamp(os.path.getmtime(screenshot_path))
        
        with cols[i % 3]:
            st.image(
                screenshot_path,
                caption=f"{screenshot_file}\n{file_time.strftime('%H:%M:%S')}",
                use_container_width=True
            )
            
            # 提供下载按钮
            with open(screenshot_path, "rb") as file:
                st.download_button(
                    label="📥 下载",
                    data=file.read(),
                    file_name=screenshot_file,
                    mime="image/png",
                    key=f"download_{screenshot_file}"
                )


def render_execution_log(workflow_state: WorkflowState):
    """
    渲染执行日志
    
    Args:
        workflow_state: 工作流状态
    """
    if not workflow_state.execution_steps:
        st.info("暂无执行日志")
        return
    
    st.markdown(f"#### 📝 执行日志 (共{len(workflow_state.execution_steps)}条)")
    
    # 提供过滤选项
    filter_node = st.selectbox(
        "筛选节点",
        ["全部"] + list(set(step.node for step in workflow_state.execution_steps)),
        key="log_filter"
    )
    
    # 过滤步骤
    filtered_steps = workflow_state.execution_steps
    if filter_node != "全部":
        filtered_steps = [s for s in filtered_steps if s.node == filter_node]
    
    # 显示日志
    log_content = []
    for step in filtered_steps:
        time_str = step.timestamp.strftime("%H:%M:%S.%f")[:-3]
        status_icon = "✅" if step.status == "completed" else "❌"
        log_content.append(
            f"[{time_str}] {status_icon} [{step.node.upper()}] {step.action}: {step.content[:200]}"
        )
    
    # 使用代码块显示日志
    st.code("\n".join(log_content), language="text")
    
    # 提供下载选项
    st.download_button(
        label="📥 下载完整日志",
        data="\n".join(log_content),
        file_name=f"execution_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
        mime="text/plain"
    )


def render_raw_output(workflow_state: WorkflowState):
    """
    渲染原始输出
    
    Args:
        workflow_state: 工作流状态
    """
    if workflow_state.final_result:
        st.markdown("#### 🔍 原始输出数据")
        
        # 格式化JSON显示
        if isinstance(workflow_state.final_result, dict):
            st.json(workflow_state.final_result)
        else:
            st.code(str(workflow_state.final_result), language="text")
    else:
        st.info("暂无原始输出数据")


def display_formatted_content(content: Any):
    """
    根据内容类型智能格式化显示
    
    Args:
        content: 要显示的内容
    """
    if isinstance(content, str):
        # 尝试解析为JSON
        try:
            json_data = json.loads(content)
            display_json_as_readable(json_data)
        except:
            # 检查是否是HTML内容
            if any(tag in content.lower() for tag in ["<html", "<div", "<span", "<p>", "<h1", "<h2"]):
                # 提取文本内容
                text_content = extract_text_from_html(content)
                if text_content:
                    st.markdown("**提取的内容：**")
                    st.text_area("", text_content, height=min(300, len(text_content) // 4))
                else:
                    st.code(content[:1000] + "..." if len(content) > 1000 else content, language="html")
            # 检查是否是代码
            elif any(keyword in content for keyword in ["def ", "class ", "import ", "function"]):
                st.code(content, language="python")
            else:
                # 普通文本 - 格式化显示
                if len(content) > 500:
                    st.text_area("", content[:500] + "...\n\n[内容已截断]", height=200)
                else:
                    st.text_area("", content, height=min(300, len(content) // 3))
    elif isinstance(content, (dict, list)):
        display_json_as_readable(content)
    else:
        st.text(str(content))


def display_json_as_readable(data: Any):
    """
    将JSON数据转换为可读格式显示
    
    Args:
        data: JSON数据
    """
    if isinstance(data, dict):
        # 提取关键字段
        if "task_completed" in data:
            st.success(f"✅ 任务完成: {data.get('task_completed', 'Unknown')}")
        
        if "extracted_data" in data:
            st.markdown("**📊 提取的数据：**")
            extracted = data["extracted_data"]
            if isinstance(extracted, list):
                for i, item in enumerate(extracted[:10], 1):  # 最多显示10条
                    with st.expander(f"条目 {i}", expanded=(i <= 3)):
                        format_dict_item(item)
            else:
                format_dict_item(extracted)
        
        # 搜索结果
        if "search_results" in data or "results" in data:
            results = data.get("search_results") or data.get("results", [])
            st.markdown("**🔍 搜索结果：**")
            if isinstance(results, list):
                for i, result in enumerate(results[:5], 1):  # 最多显示5条
                    format_search_result(result, i)
            else:
                st.write(results)
        
        # 页面信息
        if "page_title" in data or "title" in data:
            title = data.get("page_title") or data.get("title")
            st.markdown(f"**📄 页面标题：** {title}")
        
        if "url" in data:
            st.markdown(f"**🔗 URL：** {data['url']}")
        
        if "content" in data:
            content = data["content"]
            if isinstance(content, str):
                st.markdown("**📝 内容：**")
                st.text_area("", content[:500] + "..." if len(content) > 500 else content, height=150)
            elif isinstance(content, list):
                st.markdown("**📝 内容列表：**")
                for item in content[:5]:
                    st.write(f"• {item}")
        
        # 如果没有识别到特定格式，显示格式化的JSON
        if not any(key in data for key in ["task_completed", "extracted_data", "search_results", 
                                            "results", "page_title", "title", "content"]):
            # 过滤掉内部字段
            filtered_data = {k: v for k, v in data.items() 
                           if not k.startswith("_") and v is not None}
            if filtered_data:
                st.markdown("**📋 数据详情：**")
                for key, value in filtered_data.items():
                    if isinstance(value, (str, int, float, bool)):
                        st.write(f"**{key}:** {value}")
                    elif isinstance(value, list) and len(value) > 0:
                        st.write(f"**{key}:** {len(value)} 项")
                        with st.expander(f"查看 {key} 详情"):
                            for item in value[:5]:
                                st.write(f"• {item}")
                    elif isinstance(value, dict):
                        st.write(f"**{key}:**")
                        with st.expander(f"查看 {key} 详情"):
                            format_dict_item(value)
    
    elif isinstance(data, list):
        st.markdown(f"**📋 列表数据（共{len(data)}项）：**")
        for i, item in enumerate(data[:10], 1):  # 最多显示10项
            if isinstance(item, dict):
                with st.expander(f"项目 {i}", expanded=(i <= 3)):
                    format_dict_item(item)
            else:
                st.write(f"{i}. {item}")
    else:
        st.write(data)


def format_dict_item(item: Dict[str, Any]):
    """格式化字典项"""
    if isinstance(item, dict):
        for key, value in item.items():
            if value is not None and not key.startswith("_"):
                if isinstance(value, (str, int, float, bool)):
                    st.write(f"**{key}:** {value}")
                elif isinstance(value, list) and len(value) > 0:
                    st.write(f"**{key}:** {', '.join(str(v) for v in value[:5])}")
                elif isinstance(value, dict):
                    st.write(f"**{key}:** [嵌套对象]")
    else:
        st.write(str(item))


def format_search_result(result: Any, index: int):
    """格式化搜索结果"""
    if isinstance(result, dict):
        title = result.get("title") or result.get("name") or f"结果 {index}"
        link = result.get("link") or result.get("url") or ""
        description = result.get("description") or result.get("snippet") or ""
        
        st.markdown(f"**{index}. {title}**")
        if link:
            st.markdown(f"   🔗 {link}")
        if description:
            st.markdown(f"   📝 {description[:200]}...")
    else:
        st.write(f"{index}. {result}")


def extract_text_from_html(html_content: str) -> str:
    """从HTML中提取纯文本"""
    import re
    # 移除script和style标签及其内容
    html_content = re.sub(r'<script[^>]*>.*?</script>', '', html_content, flags=re.DOTALL | re.IGNORECASE)
    html_content = re.sub(r'<style[^>]*>.*?</style>', '', html_content, flags=re.DOTALL | re.IGNORECASE)
    # 移除HTML标签
    text = re.sub(r'<[^>]+>', ' ', html_content)
    # 清理多余空白
    text = re.sub(r'\s+', ' ', text).strip()
    return text[:1000] if len(text) > 1000 else text