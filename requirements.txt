# Core dependencies
langchain
langchain-community
langchain-openai
langgraph
playwright
python-dotenv
serpapi
langgraph-cli[inmem]
# MCP (Model Context Protocol) support - 升级到FastMCP 2.0
mcp
pytest
pydantic
pytest-asyncio
# Streamlit UI dependencies
streamlit
pandas
psutil
# Development dependencies (optional)
# Install with: pip install -r requirements-dev.txt
# pytest>=7.0.0
# pytest-asyncio>=0.21.0
# black>=23.0.0
# flake8>=6.0.0
# mypy>=1.0.0